# [DES-COMM-001] 模块协作与通信规范

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统各模块间的协作机制、通信协议、API接口标准和错误处理规范，确保系统各组件能够高效、稳定地协同工作。

## 🏗️ 模块通信架构

### [DES-COMM-001] 通信架构设计
**设计ID**：DES-COMM-001  
**架构模式**：事件驱动 + API调用 + 消息队列  
**实现需求**：模块间通信需求

**通信架构图**：
```
┌─────────────────────────────────────────┐
│              前端通信层                 │
│  ┌─────────────┬─────────────────────┐ │
│  │ 事件总线    │ API客户端           │ │
│  │ EventBus    │ HTTP Client         │ │
│  │ 组件通信    │ 服务调用            │ │
│  ├─────────────┼─────────────────────┤ │
│  │ 状态管理    │ 实时通信            │ │
│  │ Pinia Store │ WebSocket           │ │
│  │ 状态同步    │ 推送通知            │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
                    ↕ HTTP/WebSocket
┌─────────────────────────────────────────┐
│              后端通信层                 │
│  ┌─────────────┬─────────────────────┐ │
│  │ API网关     │ 事件系统            │ │
│  │ Express     │ EventEmitter        │ │
│  │ 路由分发    │ 模块通信            │ │
│  ├─────────────┼─────────────────────┤ │
│  │ 服务层      │ 消息队列            │ │
│  │ Business    │ Redis Pub/Sub       │ │
│  │ Services    │ 异步处理            │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
```

**通信协议栈**：
```typescript
// 通信管理器
class CommunicationManager {
  private eventBus: EventBus
  private apiClient: ApiClient
  private webSocketClient: WebSocketClient
  private messageQueue: MessageQueue

  constructor() {
    this.eventBus = new EventBus()
    this.apiClient = new ApiClient()
    this.webSocketClient = new WebSocketClient()
    this.messageQueue = new MessageQueue()

    this.setupCommunicationChannels()
  }

  private setupCommunicationChannels(): void {
    // 设置API客户端
    this.setupApiClient()
    
    // 设置WebSocket连接
    this.setupWebSocket()
    
    // 设置事件总线
    this.setupEventBus()
    
    // 设置消息队列
    this.setupMessageQueue()
  }

  private setupApiClient(): void {
    this.apiClient.setBaseURL('/api')
    this.apiClient.setDefaultHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    })

    // 请求拦截器
    this.apiClient.interceptors.request.use((config) => {
      // 添加认证token
      const token = this.getAuthToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = this.generateRequestId()

      // 添加时间戳
      config.headers['X-Timestamp'] = new Date().toISOString()

      return config
    })

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      (response) => {
        // 处理成功响应
        this.handleSuccessResponse(response)
        return response
      },
      (error) => {
        // 处理错误响应
        this.handleErrorResponse(error)
        return Promise.reject(error)
      }
    )
  }

  private setupWebSocket(): void {
    this.webSocketClient.connect('/ws')

    // 连接事件
    this.webSocketClient.on('connect', () => {
      console.log('WebSocket connected')
      this.eventBus.emit('websocket:connected')
    })

    this.webSocketClient.on('disconnect', () => {
      console.log('WebSocket disconnected')
      this.eventBus.emit('websocket:disconnected')
    })

    // 消息处理
    this.webSocketClient.on('message', (message) => {
      this.handleWebSocketMessage(message)
    })

    // 错误处理
    this.webSocketClient.on('error', (error) => {
      console.error('WebSocket error:', error)
      this.eventBus.emit('websocket:error', error)
    })
  }

  private setupEventBus(): void {
    // 设置事件总线的错误处理
    this.eventBus.on('error', (error) => {
      console.error('EventBus error:', error)
    })

    // 设置事件日志
    this.eventBus.on('*', (eventName, ...args) => {
      console.debug(`Event: ${eventName}`, args)
    })
  }

  private setupMessageQueue(): void {
    // 设置消息队列处理器
    this.messageQueue.on('message', (message) => {
      this.handleQueueMessage(message)
    })

    this.messageQueue.on('error', (error) => {
      console.error('Message queue error:', error)
    })
  }

  // API通信方法
  async sendApiRequest<T>(
    method: HttpMethod,
    url: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.apiClient.request<T>({
        method,
        url,
        data,
        ...options
      })

      return {
        success: true,
        data: response.data,
        message: 'Request successful',
        requestId: response.headers['x-request-id']
      }
    } catch (error) {
      throw this.transformApiError(error)
    }
  }

  // 事件通信方法
  emitEvent(eventName: string, payload?: any): void {
    this.eventBus.emit(eventName, payload)
  }

  onEvent(eventName: string, handler: EventHandler): void {
    this.eventBus.on(eventName, handler)
  }

  offEvent(eventName: string, handler?: EventHandler): void {
    this.eventBus.off(eventName, handler)
  }

  // WebSocket通信方法
  sendWebSocketMessage(type: string, payload: any): void {
    const message = {
      type,
      payload,
      timestamp: new Date().toISOString(),
      id: this.generateMessageId()
    }

    this.webSocketClient.send(message)
  }

  // 消息队列方法
  enqueueMessage(message: QueueMessage): void {
    this.messageQueue.enqueue(message)
  }

  // 错误处理
  private handleSuccessResponse(response: any): void {
    // 记录成功响应
    const requestId = response.headers['x-request-id']
    console.debug(`API Success [${requestId}]:`, response.status)
  }

  private handleErrorResponse(error: any): void {
    // 统一错误处理
    const errorInfo = this.extractErrorInfo(error)
    
    // 发送错误事件
    this.eventBus.emit('api:error', errorInfo)

    // 根据错误类型进行处理
    switch (errorInfo.type) {
      case 'NETWORK_ERROR':
        this.handleNetworkError(errorInfo)
        break
      case 'AUTH_ERROR':
        this.handleAuthError(errorInfo)
        break
      case 'VALIDATION_ERROR':
        this.handleValidationError(errorInfo)
        break
      case 'SERVER_ERROR':
        this.handleServerError(errorInfo)
        break
    }
  }

  private handleWebSocketMessage(message: any): void {
    try {
      const parsedMessage = JSON.parse(message)
      
      // 根据消息类型分发
      switch (parsedMessage.type) {
        case 'TASK_UPDATE':
          this.eventBus.emit('task:updated', parsedMessage.payload)
          break
        case 'REVIEW_REMINDER':
          this.eventBus.emit('review:reminder', parsedMessage.payload)
          break
        case 'SYNC_STATUS':
          this.eventBus.emit('sync:status', parsedMessage.payload)
          break
        case 'NOTIFICATION':
          this.eventBus.emit('notification:received', parsedMessage.payload)
          break
        default:
          console.warn('Unknown WebSocket message type:', parsedMessage.type)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  private handleQueueMessage(message: QueueMessage): void {
    // 处理队列消息
    switch (message.type) {
      case 'SYNC_OPERATION':
        this.processSyncOperation(message.payload)
        break
      case 'BACKGROUND_TASK':
        this.processBackgroundTask(message.payload)
        break
      case 'NOTIFICATION':
        this.processNotification(message.payload)
        break
    }
  }

  // 错误处理方法
  private handleNetworkError(error: ErrorInfo): void {
    // 网络错误处理
    this.eventBus.emit('network:error', error)
    
    // 启用离线模式
    this.eventBus.emit('app:offline_mode', true)
  }

  private handleAuthError(error: ErrorInfo): void {
    // 认证错误处理
    this.eventBus.emit('auth:error', error)
    
    // 清除认证信息
    this.clearAuthToken()
    
    // 重定向到登录页
    this.eventBus.emit('auth:redirect_login')
  }

  private handleValidationError(error: ErrorInfo): void {
    // 验证错误处理
    this.eventBus.emit('validation:error', error)
  }

  private handleServerError(error: ErrorInfo): void {
    // 服务器错误处理
    this.eventBus.emit('server:error', error)
    
    // 显示错误提示
    this.eventBus.emit('ui:show_error', {
      title: '服务器错误',
      message: '服务器暂时无法响应，请稍后重试',
      type: 'error'
    })
  }

  // 工具方法
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getAuthToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  private clearAuthToken(): void {
    localStorage.removeItem('auth_token')
  }

  private extractErrorInfo(error: any): ErrorInfo {
    return {
      type: this.determineErrorType(error),
      code: error.response?.data?.code || error.code || 'UNKNOWN_ERROR',
      message: error.response?.data?.message || error.message || '未知错误',
      details: error.response?.data?.details || {},
      requestId: error.response?.headers?.['x-request-id'],
      timestamp: new Date().toISOString()
    }
  }

  private determineErrorType(error: any): ErrorType {
    if (!error.response) {
      return 'NETWORK_ERROR'
    }

    const status = error.response.status
    
    if (status === 401 || status === 403) {
      return 'AUTH_ERROR'
    } else if (status >= 400 && status < 500) {
      return 'VALIDATION_ERROR'
    } else if (status >= 500) {
      return 'SERVER_ERROR'
    }

    return 'UNKNOWN_ERROR'
  }

  private transformApiError(error: any): ApiError {
    const errorInfo = this.extractErrorInfo(error)
    
    return new ApiError(
      errorInfo.message,
      errorInfo.code,
      errorInfo.type,
      errorInfo.details
    )
  }
}

// 类型定义
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
type ErrorType = 'NETWORK_ERROR' | 'AUTH_ERROR' | 'VALIDATION_ERROR' | 'SERVER_ERROR' | 'UNKNOWN_ERROR'
type EventHandler = (...args: any[]) => void

interface RequestOptions {
  timeout?: number
  headers?: Record<string, string>
  params?: Record<string, any>
}

interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  requestId?: string
}

interface ErrorInfo {
  type: ErrorType
  code: string
  message: string
  details: any
  requestId?: string
  timestamp: string
}

interface QueueMessage {
  id: string
  type: string
  payload: any
  priority: number
  timestamp: Date
  retryCount?: number
}

class ApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public type: ErrorType,
    public details: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}
```

**通信特性**：
- **多通道通信**：HTTP API + WebSocket + 事件总线
- **统一错误处理**：标准化的错误处理和传播机制
- **请求追踪**：完整的请求生命周期追踪
- **自动重试**：网络错误时的自动重试机制
- **离线支持**：网络断开时的优雅降级

**实现需求**：模块间通信需求  
**相关设计**：[DES-API-001] ~ [DES-API-007]

## 🔌 API接口标准

### [DES-API-STD-001] RESTful API设计规范
**设计ID**：DES-API-STD-001  
**API风格**：RESTful + JSON  
**实现需求**：API接口标准化需求

**API设计规范**：
```typescript
// API路由设计规范
const API_ROUTES = {
  // 任务管理API
  tasks: {
    list: 'GET /api/tasks',
    create: 'POST /api/tasks',
    get: 'GET /api/tasks/:id',
    update: 'PUT /api/tasks/:id',
    delete: 'DELETE /api/tasks/:id',
    updateStatus: 'PATCH /api/tasks/:id/status'
  },

  // 复习管理API
  reviews: {
    list: 'GET /api/reviews',
    start: 'POST /api/reviews/:id/start',
    complete: 'POST /api/reviews/:id/complete',
    skip: 'POST /api/reviews/:id/skip',
    upcoming: 'GET /api/reviews/upcoming'
  },

  // 思维导图API
  mindmaps: {
    list: 'GET /api/mindmaps',
    create: 'POST /api/mindmaps',
    get: 'GET /api/mindmaps/:id',
    update: 'PUT /api/mindmaps/:id',
    delete: 'DELETE /api/mindmaps/:id',
    export: 'GET /api/mindmaps/:id/export'
  },

  // 任务关联API
  associations: {
    create: 'POST /api/mindmaps/:mindMapId/nodes/:nodeId/associate-task',
    batchCreate: 'POST /api/mindmaps/:mindMapId/batch-create-tasks',
    syncStatus: 'GET /api/associations/:id/sync-status',
    resolveConflict: 'POST /api/associations/:id/resolve-conflict'
  },

  // 负载均衡API
  loadBalance: {
    check: 'POST /api/load-balance/check',
    trend: 'GET /api/load-balance/trend',
    optimize: 'POST /api/load-balance/optimize'
  },

  // 时间预估API
  timeEstimation: {
    estimate: 'POST /api/time-estimation/estimate',
    updateRecord: 'POST /api/time-estimation/update-record',
    efficiency: 'GET /api/time-estimation/efficiency'
  },

  // 数据同步API
  sync: {
    changes: 'GET /api/sync/changes',
    upload: 'POST /api/sync/upload',
    status: 'GET /api/sync/status',
    resolve: 'POST /api/sync/resolve-conflict'
  },

  // 用户管理API
  users: {
    profile: 'GET /api/users/profile',
    updateProfile: 'PUT /api/users/profile',
    preferences: 'GET /api/users/preferences',
    updatePreferences: 'PUT /api/users/preferences'
  }
}

// 标准响应格式
interface StandardApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    requestId: string
    timestamp: string
    version: string
  }
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 标准错误码
const ERROR_CODES = {
  // 通用错误码 (1000-1999)
  INVALID_REQUEST: '1001',
  MISSING_PARAMETER: '1002',
  INVALID_PARAMETER: '1003',
  RESOURCE_NOT_FOUND: '1004',
  PERMISSION_DENIED: '1005',
  RATE_LIMIT_EXCEEDED: '1006',

  // 认证错误码 (2000-2999)
  UNAUTHORIZED: '2001',
  TOKEN_EXPIRED: '2002',
  TOKEN_INVALID: '2003',
  LOGIN_REQUIRED: '2004',

  // 业务错误码 (3000-3999)
  TASK_NOT_FOUND: '3001',
  TASK_ALREADY_COMPLETED: '3002',
  REVIEW_NOT_AVAILABLE: '3003',
  MINDMAP_NODE_NOT_FOUND: '3004',
  ASSOCIATION_ALREADY_EXISTS: '3005',
  LOAD_BALANCE_EXCEEDED: '3006',
  SYNC_CONFLICT: '3007',

  // 系统错误码 (5000-5999)
  INTERNAL_SERVER_ERROR: '5001',
  DATABASE_ERROR: '5002',
  EXTERNAL_SERVICE_ERROR: '5003',
  MAINTENANCE_MODE: '5004'
}

// API中间件
class ApiMiddleware {
  // 请求验证中间件
  static validateRequest(schema: any) {
    return (req: Request, res: Response, next: NextFunction) => {
      const { error } = schema.validate(req.body)
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.INVALID_PARAMETER,
            message: '请求参数验证失败',
            details: error.details
          },
          meta: {
            requestId: req.headers['x-request-id'],
            timestamp: new Date().toISOString(),
            version: '1.0'
          }
        })
      }
      
      next()
    }
  }

  // 认证中间件
  static authenticate(req: Request, res: Response, next: NextFunction) {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: ERROR_CODES.LOGIN_REQUIRED,
          message: '需要登录认证'
        }
      })
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!)
      req.user = decoded
      next()
    } catch (error) {
      return res.status(401).json({
        success: false,
        error: {
          code: ERROR_CODES.TOKEN_INVALID,
          message: '认证令牌无效'
        }
      })
    }
  }

  // 权限检查中间件
  static authorize(permissions: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
      const userPermissions = req.user?.permissions || []
      
      const hasPermission = permissions.some(permission => 
        userPermissions.includes(permission)
      )

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.PERMISSION_DENIED,
            message: '权限不足'
          }
        })
      }

      next()
    }
  }

  // 限流中间件
  static rateLimit(options: RateLimitOptions) {
    const limiter = new Map<string, RateLimitInfo>()

    return (req: Request, res: Response, next: NextFunction) => {
      const key = req.ip + ':' + req.path
      const now = Date.now()
      const windowStart = now - options.windowMs

      // 清理过期记录
      const info = limiter.get(key)
      if (info) {
        info.requests = info.requests.filter(time => time > windowStart)
      }

      const currentInfo = limiter.get(key) || { requests: [] }
      currentInfo.requests.push(now)
      limiter.set(key, currentInfo)

      if (currentInfo.requests.length > options.max) {
        return res.status(429).json({
          success: false,
          error: {
            code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
            message: '请求频率过高，请稍后重试'
          }
        })
      }

      next()
    }
  }

  // 错误处理中间件
  static errorHandler(
    error: any, 
    req: Request, 
    res: Response, 
    next: NextFunction
  ) {
    console.error('API Error:', error)

    // 数据库错误
    if (error.name === 'MongoError' || error.name === 'SequelizeError') {
      return res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: '数据库操作失败'
        }
      })
    }

    // 验证错误
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: {
          code: ERROR_CODES.INVALID_PARAMETER,
          message: '数据验证失败',
          details: error.details
        }
      })
    }

    // 默认错误
    return res.status(500).json({
      success: false,
      error: {
        code: ERROR_CODES.INTERNAL_SERVER_ERROR,
        message: '服务器内部错误'
      }
    })
  }

  // 响应格式化中间件
  static formatResponse(req: Request, res: Response, next: NextFunction) {
    const originalJson = res.json

    res.json = function(data: any) {
      // 如果已经是标准格式，直接返回
      if (data && typeof data === 'object' && 'success' in data) {
        return originalJson.call(this, data)
      }

      // 格式化为标准响应
      const formattedResponse: StandardApiResponse = {
        success: true,
        data,
        meta: {
          requestId: req.headers['x-request-id'] as string,
          timestamp: new Date().toISOString(),
          version: '1.0'
        }
      }

      return originalJson.call(this, formattedResponse)
    }

    next()
  }
}

// 类型定义
interface RateLimitOptions {
  windowMs: number
  max: number
}

interface RateLimitInfo {
  requests: number[]
}
```

**API标准特性**：
- **统一格式**：标准化的请求和响应格式
- **错误处理**：完整的错误码体系和处理机制
- **安全认证**：JWT认证和权限控制
- **限流保护**：API访问频率限制
- **请求追踪**：完整的请求生命周期追踪

**实现需求**：API接口标准化需求  
**相关设计**：[DES-COMM-001] 通信架构设计

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-01-31 | 初始模块协作与通信规范创建 | 系统架构师 | 技术负责人 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：系统架构师  
**审核人**：技术负责人  
**状态**：待审核
