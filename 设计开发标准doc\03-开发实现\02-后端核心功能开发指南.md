# [DEV-BACKEND-001] 后端核心功能开发指南

## 📋 概述

本文档基于 [DES-ARCH-001] 系统整体架构设计和 [DES-API-001] API接口设计，提供艾宾浩斯记忆曲线学习管理系统后端核心功能的详细开发指南。重点实现任务管理、艾宾浩斯算法、负载均衡、API服务等核心功能模块。

## 🎯 开发目标

### 核心功能模块
- **任务管理API服务**：实现 [DES-API-001] 到 [DES-API-004] 的后端接口
- **艾宾浩斯算法服务**：基于 [DES-ALGO-001] 的复习计划生成
- **负载均衡服务**：基于 [DES-ALGO-002] 的负载检查和预警
- **认证授权服务**：基于 [DES-API-009] 的用户认证

### 技术实现目标
- 基于 [DES-TECH-002] 后端技术栈：Node.js + Express + MongoDB
- 遵循 [DES-API-PRINCIPLE-001] RESTful设计原则
- 集成 [DES-MODEL-001] 到 [DES-MODEL-005] 数据模型
- 实现 [REQ-NFUNC-002] 并发性能要求（1000并发用户）

## 🔧 开发环境配置

### [DEV-ENV-BACKEND-001] 基础环境要求
**环境ID**：DEV-ENV-BACKEND-001  
**基础要求**：
- Node.js 18+ LTS
- npm 9+ 或 yarn 1.22+
- MongoDB 6.0+ 或 Docker
- Redis 7.0+ (可选，用于缓存)
- Git 2.30+
- VS Code + Node.js相关插件

### [DEV-ENV-BACKEND-002] 项目初始化
**配置ID**：DEV-ENV-BACKEND-002  
**初始化步骤**：

```bash
# 1. 创建后端项目目录
mkdir ebbinghaus-backend
cd ebbinghaus-backend

# 2. 初始化Node.js项目
npm init -y

# 3. 安装核心依赖
npm install express mongoose cors helmet morgan
npm install jsonwebtoken bcryptjs dotenv
npm install express-rate-limit express-validator
npm install winston winston-daily-rotate-file
npm install redis ioredis
npm install dayjs uuid

# 4. 安装开发依赖
npm install -D nodemon @types/node typescript
npm install -D @types/express @types/jsonwebtoken
npm install -D @types/bcryptjs @types/cors
npm install -D eslint prettier

# 5. 创建TypeScript配置
npx tsc --init
```

### [DEV-ENV-BACKEND-003] 项目结构配置
**结构ID**：DEV-ENV-BACKEND-003  
**目录结构**：

```
src/
├── config/                 # 配置文件
│   ├── database.js         # 数据库配置
│   ├── redis.js           # Redis配置
│   └── logger.js          # 日志配置
├── controllers/            # 控制器
│   ├── taskController.js   # 任务管理控制器
│   ├── reviewController.js # 复习管理控制器
│   ├── authController.js   # 认证控制器
│   └── analyticsController.js # 分析控制器
├── services/              # 业务服务
│   ├── taskService.js     # 任务服务
│   ├── ebbinghausService.js # 艾宾浩斯算法服务
│   ├── loadBalanceService.js # 负载均衡服务
│   └── authService.js     # 认证服务
├── models/                # 数据模型
│   ├── Task.js           # 任务模型
│   ├── ReviewSchedule.js # 复习计划模型
│   ├── User.js           # 用户模型
│   └── LearningRecord.js # 学习记录模型
├── routes/                # 路由定义
│   ├── tasks.js          # 任务路由
│   ├── reviews.js        # 复习路由
│   ├── auth.js           # 认证路由
│   └── analytics.js      # 分析路由
├── middleware/            # 中间件
│   ├── auth.js           # 认证中间件
│   ├── validation.js     # 数据验证中间件
│   ├── errorHandler.js   # 错误处理中间件
│   └── rateLimit.js      # 限流中间件
├── utils/                 # 工具函数
│   ├── constants.js      # 常量定义
│   ├── helpers.js        # 辅助函数
│   └── validators.js     # 验证器
└── app.js                # 应用入口
```

## 🏗️ 核心服务实现

### [DEV-SERVICE-001] 艾宾浩斯算法服务
**服务ID**：DEV-SERVICE-001  
**实现需求**：[DES-ALGO-001] 艾宾浩斯记忆曲线算法  
**对应API**：[DES-API-001] 创建学习任务

#### ebbinghausService.js 服务实现
```javascript
// src/services/ebbinghausService.js
const dayjs = require('dayjs');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');

class EbbinghausService {
  constructor() {
    // 标准艾宾浩斯间隔（分钟）
    this.STANDARD_INTERVALS = [
      5,        // 5分钟
      30,       // 30分钟
      720,      // 12小时
      1440,     // 1天
      4320,     // 3天
      10080,    // 1周
      20160,    // 2周
      43200,    // 1月
      86400     // 2月
    ];
  }

  /**
   * 生成复习计划
   * @param {string} taskId - 任务ID
   * @param {Date} createdAt - 任务创建时间
   * @param {Object} userPreferences - 用户偏好设置
   * @returns {Array} 复习计划数组
   */
  generateReviewSchedule(taskId, createdAt, userPreferences = {}) {
    try {
      const schedule = [];
      const baseTime = dayjs(createdAt);

      for (let i = 0; i < this.STANDARD_INTERVALS.length; i++) {
        const intervalMinutes = this.STANDARD_INTERVALS[i];
        let reviewTime = baseTime.add(intervalMinutes, 'minute');

        // 应用用户偏好调整
        if (userPreferences.preferredTimes) {
          reviewTime = this.adjustForUserPreferences(reviewTime, userPreferences);
        }

        // 避开休息时间
        if (userPreferences.restHours) {
          reviewTime = this.avoidRestTime(reviewTime, userPreferences.restHours);
        }

        schedule.push({
          id: uuidv4(),
          taskId,
          reviewIndex: i + 1,
          intervalDays: this.minutesToDays(intervalMinutes),
          scheduledTime: reviewTime.toDate(),
          status: 'scheduled',
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      logger.info(`Generated review schedule for task ${taskId}`, {
        taskId,
        scheduleCount: schedule.length
      });

      return schedule;
    } catch (error) {
      logger.error('Error generating review schedule', {
        taskId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 根据复习效果调整后续复习间隔
   * @param {Array} schedule - 复习计划数组
   * @param {number} completedReviewIndex - 已完成的复习索引
   * @param {number} effectiveness - 复习效果评分（1-5）
   * @returns {Array} 调整后的复习计划
   */
  adjustScheduleByEffectiveness(schedule, completedReviewIndex, effectiveness) {
    try {
      const adjustmentFactor = this.calculateAdjustmentFactor(effectiveness);
      
      // 调整后续复习时间
      for (let i = completedReviewIndex; i < schedule.length; i++) {
        if (i === 0) continue; // 跳过第一个复习

        const prevTime = dayjs(schedule[i - 1].scheduledTime);
        const currentTime = dayjs(schedule[i].scheduledTime);
        const currentInterval = currentTime.diff(prevTime, 'minute');
        const adjustedInterval = Math.round(currentInterval * adjustmentFactor);

        schedule[i].scheduledTime = prevTime.add(adjustedInterval, 'minute').toDate();
        schedule[i].updatedAt = new Date();
      }

      logger.info('Adjusted review schedule based on effectiveness', {
        completedReviewIndex,
        effectiveness,
        adjustmentFactor
      });

      return schedule;
    } catch (error) {
      logger.error('Error adjusting review schedule', {
        completedReviewIndex,
        effectiveness,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 计算调整系数
   * @param {number} effectiveness - 复习效果评分（1-5）
   * @returns {number} 调整系数
   */
  calculateAdjustmentFactor(effectiveness) {
    const factorMap = {
      1: 0.7,  // 效果很差，大幅缩短间隔
      2: 0.85, // 效果较差，适度缩短间隔
      3: 1.0,  // 效果一般，保持原间隔
      4: 1.2,  // 效果较好，适度延长间隔
      5: 1.4   // 效果很好，大幅延长间隔
    };
    return factorMap[effectiveness] || 1.0;
  }

  /**
   * 根据用户偏好调整复习时间
   * @param {Object} reviewTime - dayjs时间对象
   * @param {Object} userPreferences - 用户偏好
   * @returns {Object} 调整后的时间
   */
  adjustForUserPreferences(reviewTime, userPreferences) {
    if (!userPreferences.preferredTimes || userPreferences.preferredTimes.length === 0) {
      return reviewTime;
    }

    // 找到最接近的偏好时间
    const preferredHours = userPreferences.preferredTimes.map(time => {
      const [hour, minute] = time.split(':').map(Number);
      return hour + minute / 60;
    });

    const currentHour = reviewTime.hour() + reviewTime.minute() / 60;
    const closestHour = preferredHours.reduce((prev, curr) => 
      Math.abs(curr - currentHour) < Math.abs(prev - currentHour) ? curr : prev
    );

    const targetHour = Math.floor(closestHour);
    const targetMinute = Math.round((closestHour - targetHour) * 60);

    return reviewTime.hour(targetHour).minute(targetMinute).second(0);
  }

  /**
   * 避开休息时间
   * @param {Object} reviewTime - dayjs时间对象
   * @param {Array} restHours - 休息时间段
   * @returns {Object} 调整后的时间
   */
  avoidRestTime(reviewTime, restHours) {
    if (!restHours || restHours.length === 0) {
      return reviewTime;
    }

    const currentHour = reviewTime.hour();
    
    for (const restPeriod of restHours) {
      const [startHour, endHour] = restPeriod.split('-').map(Number);
      
      if (currentHour >= startHour && currentHour < endHour) {
        // 如果在休息时间内，调整到休息时间结束后
        return reviewTime.hour(endHour).minute(0).second(0);
      }
    }

    return reviewTime;
  }

  /**
   * 分钟转换为天数
   * @param {number} minutes - 分钟数
   * @returns {number} 天数
   */
  minutesToDays(minutes) {
    return Math.round((minutes / 1440) * 1000) / 1000; // 保留3位小数
  }
}

module.exports = new EbbinghausService();
```

### [DEV-SERVICE-002] 负载均衡服务
**服务ID**：DEV-SERVICE-002  
**实现需求**：[DES-ALGO-002] 负载均衡计算算法  
**对应API**：[DES-API-002] 负载均衡检查

#### loadBalanceService.js 服务实现
```javascript
// src/services/loadBalanceService.js
const dayjs = require('dayjs');
const Task = require('../models/Task');
const ReviewSchedule = require('../models/ReviewSchedule');
const User = require('../models/User');
const logger = require('../config/logger');

class LoadBalanceService {
  constructor() {
    this.LOAD_LEVELS = {
      LIGHT: 'light',
      MEDIUM: 'medium', 
      HEAVY: 'heavy'
    };
  }

  /**
   * 计算指定日期的学习负载
   * @param {Date} targetDate - 目标日期
   * @param {number} newTaskDuration - 新任务预估时长（分钟）
   * @param {string} userId - 用户ID
   * @returns {Object} 负载分析结果
   */
  async calculateDailyLoad(targetDate, newTaskDuration, userId) {
    try {
      const startOfDay = dayjs(targetDate).startOf('day').toDate();
      const endOfDay = dayjs(targetDate).endOf('day').toDate();

      // 1. 获取当日已安排的任务和复习
      const [existingTasks, existingReviews, user] = await Promise.all([
        this.getScheduledTasks(startOfDay, endOfDay, userId),
        this.getScheduledReviews(startOfDay, endOfDay, userId),
        User.findById(userId)
      ]);

      // 2. 计算现有负载
      const existingTaskLoad = existingTasks.reduce((sum, task) => 
        sum + (task.estimatedTime || 0), 0);
      const existingReviewLoad = existingReviews.reduce((sum, review) => 
        sum + this.estimateReviewTime(review), 0);

      // 3. 计算总负载
      const totalLoad = existingTaskLoad + existingReviewLoad + newTaskDuration;

      // 4. 获取用户每日学习时间限制
      const userDailyLimit = this.getUserDailyLimit(user);

      // 5. 计算负载百分比
      const loadPercentage = (totalLoad / userDailyLimit) * 100;

      // 6. 确定负载等级
      const loadLevel = this.determineLoadLevel(loadPercentage);

      // 7. 生成调整建议
      const suggestions = this.generateSuggestions(loadLevel, loadPercentage, targetDate);

      // 8. 提供替代日期
      const alternativeDates = await this.findAlternativeDates(
        newTaskDuration, 
        userId, 
        targetDate
      );

      const result = {
        targetDate: targetDate.toISOString().split('T')[0],
        loadLevel,
        currentLoad: Math.round(loadPercentage),
        totalMinutes: totalLoad,
        breakdown: {
          existingTasks: existingTaskLoad,
          existingReviews: existingReviewLoad,
          newTask: newTaskDuration
        },
        suggestions,
        alternativeDates,
        userDailyLimit
      };

      logger.info('Calculated daily load', {
        userId,
        targetDate: result.targetDate,
        loadLevel,
        totalLoad
      });

      return result;
    } catch (error) {
      logger.error('Error calculating daily load', {
        userId,
        targetDate,
        newTaskDuration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取指定时间段内的已安排任务
   */
  async getScheduledTasks(startDate, endDate, userId) {
    return await Task.find({
      userId,
      createdAt: { $gte: startDate, $lte: endDate },
      status: { $in: ['pending', 'in_progress'] }
    });
  }

  /**
   * 获取指定时间段内的已安排复习
   */
  async getScheduledReviews(startDate, endDate, userId) {
    return await ReviewSchedule.find({
      userId,
      scheduledTime: { $gte: startDate, $lte: endDate },
      status: { $in: ['scheduled', 'in_progress'] }
    }).populate('taskId');
  }

  /**
   * 估算复习时间
   */
  estimateReviewTime(review) {
    // 基于任务难度和复习次数估算复习时间
    const baseTime = review.taskId?.estimatedTime || 30;
    const difficultyFactor = (review.taskId?.difficulty || 3) / 5;
    const reviewFactor = Math.max(0.3, 1 - (review.reviewIndex - 1) * 0.1);
    
    return Math.round(baseTime * difficultyFactor * reviewFactor);
  }

  /**
   * 获取用户每日学习时间限制
   */
  getUserDailyLimit(user) {
    return user?.preferences?.dailyStudyLimit || 120; // 默认120分钟
  }

  /**
   * 确定负载等级
   */
  determineLoadLevel(loadPercentage) {
    if (loadPercentage <= 30) return this.LOAD_LEVELS.LIGHT;
    if (loadPercentage <= 60) return this.LOAD_LEVELS.MEDIUM;
    return this.LOAD_LEVELS.HEAVY;
  }

  /**
   * 生成调整建议
   */
  generateSuggestions(loadLevel, loadPercentage, targetDate) {
    const suggestions = [];
    
    if (loadLevel === this.LOAD_LEVELS.HEAVY) {
      suggestions.push("当日学习负载过重，建议调整到其他日期");
      suggestions.push("可以降低部分任务的优先级");
      suggestions.push("考虑将大任务拆分为多个小任务");
      
      if (loadPercentage > 80) {
        suggestions.push("强烈建议重新安排时间，避免学习效果下降");
      }
    } else if (loadLevel === this.LOAD_LEVELS.MEDIUM) {
      suggestions.push("当日学习负载适中，注意合理安排休息时间");
      suggestions.push("可以考虑调整任务执行顺序");
    } else {
      suggestions.push("当日学习负载较轻，可以适当增加学习内容");
    }
    
    return suggestions;
  }

  /**
   * 查找替代日期
   */
  async findAlternativeDates(taskDuration, userId, excludeDate, daysToCheck = 7) {
    const alternatives = [];
    
    for (let i = 1; i <= daysToCheck; i++) {
      const checkDate = dayjs(excludeDate).add(i, 'day').toDate();
      
      const loadResult = await this.calculateDailyLoad(checkDate, taskDuration, userId);
      
      if (loadResult.loadLevel !== this.LOAD_LEVELS.HEAVY) {
        alternatives.push({
          date: checkDate.toISOString().split('T')[0],
          loadLevel: loadResult.loadLevel,
          loadPercentage: loadResult.currentLoad
        });
      }
      
      // 最多返回3个替代日期
      if (alternatives.length >= 3) break;
    }
    
    return alternatives;
  }
}

module.exports = new LoadBalanceService();
```

## 📊 数据模型实现

### [DEV-MODEL-001] 任务数据模型
**模型ID**：DEV-MODEL-001
**基于设计**：[DES-MODEL-001] 学习任务模型

#### Task.js 模型实现
```javascript
// src/models/Task.js
const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  taskId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  content: {
    text: {
      type: String,
      required: true,
      maxlength: 5000
    },
    images: [{
      type: String // 图片URL
    }],
    audio: {
      type: String // 音频URL
    },
    attachments: [{
      type: String // 附件URL
    }]
  },
  metadata: {
    subject: {
      type: String,
      required: true,
      enum: ['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics']
    },
    estimatedTime: {
      type: Number,
      required: true,
      min: 1,
      max: 300 // 最大5小时
    },
    actualTime: {
      type: Number,
      min: 0
    },
    priority: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      default: 3
    },
    difficulty: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      default: 3
    },
    tags: [{
      type: String,
      trim: true
    }]
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'cancelled'],
    default: 'pending',
    index: true
  },
  progress: {
    completedAt: Date,
    startedAt: Date,
    notes: {
      type: String,
      maxlength: 1000
    }
  },
  // 搜索文本字段
  searchText: {
    type: String,
    index: 'text'
  },
  // 关联信息
  mindMapId: {
    type: String,
    index: true
  },
  parentTaskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    index: true
  }
}, {
  timestamps: true, // 自动添加createdAt和updatedAt
  collection: 'tasks'
});

// 复合索引
taskSchema.index({ userId: 1, status: 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.subject': 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.priority': 1, createdAt: -1 });

// 中间件：保存前更新搜索文本
taskSchema.pre('save', function(next) {
  this.searchText = `${this.title} ${this.content.text}`.toLowerCase();
  next();
});

// 虚拟字段：任务完成率
taskSchema.virtual('completionRate').get(function() {
  if (this.status === 'completed') return 100;
  if (this.status === 'in_progress') return 50;
  return 0;
});

// 实例方法：更新任务状态
taskSchema.methods.updateStatus = function(newStatus, notes = '') {
  this.status = newStatus;

  if (newStatus === 'in_progress' && !this.progress.startedAt) {
    this.progress.startedAt = new Date();
  } else if (newStatus === 'completed') {
    this.progress.completedAt = new Date();
    if (this.progress.startedAt) {
      this.metadata.actualTime = Math.round(
        (this.progress.completedAt - this.progress.startedAt) / (1000 * 60)
      );
    }
  }

  if (notes) {
    this.progress.notes = notes;
  }

  return this.save();
};

module.exports = mongoose.model('Task', taskSchema);
```

### [DEV-MODEL-002] 复习计划数据模型
**模型ID**：DEV-MODEL-002
**基于设计**：[DES-MODEL-002] 复习计划模型

#### ReviewSchedule.js 模型实现
```javascript
// src/models/ReviewSchedule.js
const mongoose = require('mongoose');

const reviewScheduleSchema = new mongoose.Schema({
  scheduleId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  taskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  reviewIndex: {
    type: Number,
    required: true,
    min: 1,
    max: 9
  },
  intervalDays: {
    type: Number,
    required: true
  },
  scheduledTime: {
    type: Date,
    required: true,
    index: true
  },
  actualTime: Date,
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'skipped', 'overdue'],
    default: 'scheduled',
    index: true
  },
  effectiveness: {
    type: Number,
    min: 1,
    max: 5
  },
  duration: {
    type: Number, // 实际复习时长（分钟）
    min: 0
  },
  notes: {
    type: String,
    maxlength: 500
  },
  adjustedTime: Date // 调整后的时间
}, {
  timestamps: true,
  collection: 'review_schedules'
});

// 复合索引
reviewScheduleSchema.index({ userId: 1, scheduledTime: 1 });
reviewScheduleSchema.index({ taskId: 1, reviewIndex: 1 });
reviewScheduleSchema.index({ userId: 1, status: 1, scheduledTime: 1 });

// 中间件：检查逾期状态
reviewScheduleSchema.pre('find', function() {
  this.updateOverdueStatus();
});

reviewScheduleSchema.pre('findOne', function() {
  this.updateOverdueStatus();
});

// 静态方法：更新逾期状态
reviewScheduleSchema.statics.updateOverdueStatus = function() {
  const now = new Date();
  return this.updateMany(
    {
      status: 'scheduled',
      scheduledTime: { $lt: now }
    },
    {
      $set: { status: 'overdue' }
    }
  );
};

// 实例方法：开始复习
reviewScheduleSchema.methods.startReview = function() {
  this.status = 'in_progress';
  this.actualTime = new Date();
  return this.save();
};

// 实例方法：完成复习
reviewScheduleSchema.methods.completeReview = function(effectiveness, notes = '') {
  const startTime = this.actualTime || this.scheduledTime;
  const endTime = new Date();

  this.status = 'completed';
  this.effectiveness = effectiveness;
  this.duration = Math.round((endTime - startTime) / (1000 * 60));
  this.notes = notes;

  return this.save();
};

module.exports = mongoose.model('ReviewSchedule', reviewScheduleSchema);
```

### [DEV-MODEL-003] 用户数据模型
**模型ID**：DEV-MODEL-003
**基于设计**：[DES-MODEL-003] 用户模型

#### User.js 模型实现
```javascript
// src/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  nickname: {
    type: String,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String // 头像URL
  },
  preferences: {
    defaultSubject: {
      type: String,
      enum: ['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics']
    },
    defaultEstimatedTime: {
      type: Number,
      default: 30,
      min: 1,
      max: 300
    },
    defaultPriority: {
      type: Number,
      default: 3,
      min: 1,
      max: 5
    },
    defaultDifficulty: {
      type: Number,
      default: 3,
      min: 1,
      max: 5
    },
    dailyStudyLimit: {
      type: Number,
      default: 120, // 默认每日学习时间限制120分钟
      min: 30,
      max: 600
    },
    reviewPreferences: {
      preferredTimes: [{
        type: String, // HH:mm 格式
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, '时间格式应为HH:mm']
      }],
      maxDailyReviews: {
        type: Number,
        default: 10,
        min: 1,
        max: 50
      },
      reviewInterval: {
        type: String,
        enum: ['strict', 'flexible'],
        default: 'flexible'
      },
      restHours: [{
        type: String, // 格式：'22-6' 表示22点到6点为休息时间
        match: [/^([0-1]?[0-9]|2[0-3])-([0-1]?[0-9]|2[0-3])$/, '休息时间格式应为HH-HH']
      }]
    },
    theme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light'
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    timezone: {
      type: String,
      default: 'Asia/Shanghai'
    }
  },
  statistics: {
    totalTasks: {
      type: Number,
      default: 0
    },
    completedTasks: {
      type: Number,
      default: 0
    },
    totalStudyTime: {
      type: Number,
      default: 0 // 总学习时间（分钟）
    },
    totalReviewTime: {
      type: Number,
      default: 0 // 总复习时间（分钟）
    },
    averageEffectiveness: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    currentStreak: {
      type: Number,
      default: 0 // 当前连续学习天数
    },
    longestStreak: {
      type: Number,
      default: 0 // 最长连续学习天数
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginAt: Date,
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date
}, {
  timestamps: true,
  collection: 'users'
});

// 索引
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ userId: 1 });

// 中间件：保存前加密密码
userSchema.pre('save', async function(next) {
  // 只有密码被修改时才加密
  if (!this.isModified('password')) return next();

  try {
    // 生成盐值并加密密码
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 实例方法：验证密码
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 实例方法：更新统计信息
userSchema.methods.updateStatistics = function(updates) {
  Object.keys(updates).forEach(key => {
    if (this.statistics[key] !== undefined) {
      this.statistics[key] = updates[key];
    }
  });
  return this.save();
};

// 实例方法：获取安全的用户信息（不包含密码）
userSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.emailVerificationToken;
  delete userObject.passwordResetToken;
  delete userObject.passwordResetExpires;
  return userObject;
};

// 虚拟字段：完成率
userSchema.virtual('completionRate').get(function() {
  if (this.statistics.totalTasks === 0) return 0;
  return Math.round((this.statistics.completedTasks / this.statistics.totalTasks) * 100);
});

// 静态方法：查找活跃用户
userSchema.statics.findActiveUsers = function() {
  return this.find({ isActive: true });
};

module.exports = mongoose.model('User', userSchema);
```

## 🎮 API控制器实现

### [DEV-CONTROLLER-001] 任务管理控制器
**控制器ID**：DEV-CONTROLLER-001
**实现API**：[DES-API-001] 到 [DES-API-004] 任务管理API

#### taskController.js 控制器实现
```javascript
// src/controllers/taskController.js
const { validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Task = require('../models/Task');
const ReviewSchedule = require('../models/ReviewSchedule');
const ebbinghausService = require('../services/ebbinghausService');
const loadBalanceService = require('../services/loadBalanceService');
const logger = require('../config/logger');

class TaskController {
  /**
   * 创建学习任务
   * POST /api/tasks
   */
  async createTask(req, res) {
    try {
      // 验证输入数据
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: '输入数据验证失败',
            details: errors.array()
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      const { title, content, subject, estimatedTime, priority, difficulty, tags, mindMapId, parentTaskId } = req.body;
      const userId = req.user.id;

      // 检查负载均衡
      const targetDate = new Date();
      const loadResult = await loadBalanceService.calculateDailyLoad(
        targetDate,
        estimatedTime,
        userId
      );

      // 如果负载过重，返回警告
      if (loadResult.loadLevel === 'heavy') {
        return res.status(422).json({
          success: false,
          error: {
            code: 'LOAD_EXCEEDED',
            message: '学习负载超限',
            details: loadResult
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 创建任务
      const taskId = uuidv4();
      const task = new Task({
        taskId,
        userId,
        title,
        content: {
          text: content,
          images: req.files?.images?.map(file => `/uploads/images/${file.filename}`) || [],
          audio: req.files?.audio?.[0] ? `/uploads/audio/${req.files.audio[0].filename}` : '',
          attachments: req.files?.attachments?.map(file => `/uploads/attachments/${file.filename}`) || []
        },
        metadata: {
          subject,
          estimatedTime,
          priority: priority || 3,
          difficulty: difficulty || 3,
          tags: tags || []
        },
        mindMapId: mindMapId || undefined,
        parentTaskId: parentTaskId || undefined
      });

      await task.save();

      // 生成复习计划
      const reviewSchedule = ebbinghausService.generateReviewSchedule(
        task._id,
        task.createdAt,
        req.user.preferences
      );

      // 保存复习计划
      const savedSchedules = await ReviewSchedule.insertMany(
        reviewSchedule.map(schedule => ({
          ...schedule,
          scheduleId: uuidv4(),
          taskId: task._id,
          userId
        }))
      );

      logger.info('Task created successfully', {
        taskId,
        userId,
        subject,
        estimatedTime
      });

      res.status(201).json({
        success: true,
        data: {
          task: {
            id: task._id,
            taskId: task.taskId,
            title: task.title,
            content: task.content,
            metadata: task.metadata,
            status: task.status,
            progress: task.progress,
            mindMapId: task.mindMapId,
            parentTaskId: task.parentTaskId,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt
          },
          reviewSchedule: savedSchedules.map(schedule => ({
            id: schedule._id,
            reviewTime: schedule.scheduledTime,
            reviewIndex: schedule.reviewIndex,
            status: schedule.status,
            intervalDays: schedule.intervalDays
          })),
          loadWarning: loadResult.loadLevel === 'medium' ? {
            level: loadResult.loadLevel,
            message: '当日学习负载适中，注意合理安排时间',
            suggestions: loadResult.suggestions
          } : null
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error creating task', {
        userId: req.user?.id,
        error: error.message,
        stack: error.stack
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '服务器内部错误',
          details: process.env.NODE_ENV === 'development' ? error.message : undefined
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }

  /**
   * 获取任务详情
   * GET /api/tasks/:taskId
   */
  async getTaskDetail(req, res) {
    try {
      const { taskId } = req.params;
      const userId = req.user.id;

      // 查找任务
      const task = await Task.findOne({ taskId, userId });
      if (!task) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: '任务不存在'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 获取复习计划
      const ReviewSchedule = require('../models/ReviewSchedule');
      const reviewSchedule = await ReviewSchedule.find({ taskId: task._id })
        .sort({ reviewIndex: 1 });

      res.json({
        success: true,
        data: {
          task: {
            id: task._id,
            taskId: task.taskId,
            title: task.title,
            content: task.content,
            metadata: task.metadata,
            status: task.status,
            progress: task.progress,
            mindMapId: task.mindMapId,
            parentTaskId: task.parentTaskId,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt
          },
          reviewSchedule: reviewSchedule.map(schedule => ({
            id: schedule._id,
            reviewTime: schedule.scheduledTime,
            reviewIndex: schedule.reviewIndex,
            status: schedule.status,
            intervalDays: schedule.intervalDays
          }))
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error getting task detail', {
        taskId: req.params.taskId,
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取任务详情失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }

  /**
   * 获取任务列表
   * GET /api/tasks
   */
  async getTaskList(req, res) {
    try {
      const {
        subject,
        status,
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        search
      } = req.query;

      const userId = req.user.id;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = { userId };

      if (subject) query['metadata.subject'] = subject;
      if (status) query.status = status;
      if (search) {
        query.$text = { $search: search };
      }

      // 构建排序条件
      const sort = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // 执行查询
      const [tasks, totalCount] = await Promise.all([
        Task.find(query)
          .sort(sort)
          .skip(skip)
          .limit(parseInt(limit))
          .select('-searchText'),
        Task.countDocuments(query)
      ]);

      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          tasks: tasks.map(task => ({
            taskId: task.taskId,
            title: task.title,
            subject: task.metadata.subject,
            status: task.status,
            createdAt: task.createdAt,
            estimatedTime: task.metadata.estimatedTime,
            priority: task.metadata.priority,
            difficulty: task.metadata.difficulty
          })),
          totalCount,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages
          }
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error getting task list', {
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取任务列表失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }

  /**
   * 更新任务
   * PUT /api/tasks/:taskId
   */
  async updateTask(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: '输入数据验证失败',
            details: errors.array()
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      const { taskId } = req.params;
      const userId = req.user.id;
      const updateData = req.body;

      // 查找任务
      const task = await Task.findOne({ taskId, userId });
      if (!task) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: '任务不存在'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 更新任务字段
      if (updateData.title !== undefined) task.title = updateData.title;
      if (updateData.content !== undefined) {
        task.content.text = updateData.content;
        // 处理文件上传
        if (req.files?.images) {
          task.content.images = req.files.images.map(file => `/uploads/images/${file.filename}`);
        }
        if (req.files?.audio?.[0]) {
          task.content.audio = `/uploads/audio/${req.files.audio[0].filename}`;
        }
        if (req.files?.attachments) {
          task.content.attachments = req.files.attachments.map(file => `/uploads/attachments/${file.filename}`);
        }
      }
      if (updateData.subject !== undefined) task.metadata.subject = updateData.subject;
      if (updateData.estimatedTime !== undefined) task.metadata.estimatedTime = updateData.estimatedTime;
      if (updateData.priority !== undefined) task.metadata.priority = updateData.priority;
      if (updateData.difficulty !== undefined) task.metadata.difficulty = updateData.difficulty;
      if (updateData.tags !== undefined) task.metadata.tags = updateData.tags;
      if (updateData.mindMapId !== undefined) task.mindMapId = updateData.mindMapId;
      if (updateData.parentTaskId !== undefined) task.parentTaskId = updateData.parentTaskId;

      await task.save();

      logger.info('Task updated successfully', {
        taskId,
        userId,
        updatedFields: Object.keys(updateData)
      });

      res.json({
        success: true,
        data: {
          task: {
            id: task._id,
            taskId: task.taskId,
            title: task.title,
            content: task.content,
            metadata: task.metadata,
            status: task.status,
            progress: task.progress,
            mindMapId: task.mindMapId,
            parentTaskId: task.parentTaskId,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt
          }
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error updating task', {
        taskId: req.params.taskId,
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '更新任务失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }

  /**
   * 删除任务
   * DELETE /api/tasks/:taskId
   */
  async deleteTask(req, res) {
    try {
      const { taskId } = req.params;
      const userId = req.user.id;

      // 查找任务
      const task = await Task.findOne({ taskId, userId });
      if (!task) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: '任务不存在'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 删除相关的复习计划
      const ReviewSchedule = require('../models/ReviewSchedule');
      await ReviewSchedule.deleteMany({ taskId: task._id });

      // 删除任务
      await Task.deleteOne({ _id: task._id });

      logger.info('Task deleted successfully', {
        taskId,
        userId
      });

      res.json({
        success: true,
        data: {
          message: '任务删除成功'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error deleting task', {
        taskId: req.params.taskId,
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '删除任务失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }

  /**
   * 更新任务状态
   * PATCH /api/tasks/:taskId/status
   */
  async updateTaskStatus(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: '输入数据验证失败',
            details: errors.array()
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      const { taskId } = req.params;
      const { status, notes } = req.body;
      const userId = req.user.id;

      // 查找任务
      const task = await Task.findOne({ taskId, userId });
      if (!task) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: '任务不存在'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 更新任务状态
      await task.updateStatus(status, notes);

      logger.info('Task status updated', {
        taskId,
        userId,
        oldStatus: task.status,
        newStatus: status
      });

      res.json({
        success: true,
        data: {
          taskId,
          status,
          updatedAt: task.updatedAt
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Error updating task status', {
        taskId: req.params.taskId,
        userId: req.user?.id,
        error: error.message
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '更新任务状态失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }
}

module.exports = new TaskController();
```

## 🛣️ 路由配置实现

### [DEV-ROUTE-001] 任务管理路由
**路由ID**：DEV-ROUTE-001
**实现API**：[DES-API-001] 到 [DES-API-004] 任务管理API

#### tasks.js 路由实现
```javascript
// src/routes/tasks.js
const express = require('express');
const { body, query, param } = require('express-validator');
const taskController = require('../controllers/taskController');
const { authMiddleware } = require('../middleware/auth');
const rateLimitMiddleware = require('../middleware/rateLimit');
const { uploadTaskFiles } = require('../middleware/upload');

const router = express.Router();

// 应用认证中间件到所有路由
router.use(authMiddleware);

// 创建任务路由
router.post('/',
  rateLimitMiddleware.createTask, // 限流：每分钟最多5个请求
  uploadTaskFiles, // 文件上传处理
  [
    body('title')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('任务标题长度应在1-100字符之间'),
    body('content')
      .trim()
      .isLength({ min: 1, max: 5000 })
      .withMessage('任务内容长度应在1-5000字符之间'),
    body('subject')
      .isIn(['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics'])
      .withMessage('无效的学科分类'),
    body('estimatedTime')
      .isInt({ min: 1, max: 300 })
      .withMessage('预估时间应在1-300分钟之间'),
    body('priority')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('优先级应在1-5之间'),
    body('difficulty')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('难度级别应在1-5之间'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签应为数组格式')
  ],
  taskController.createTask
);

// 获取任务列表路由
router.get('/',
  [
    query('subject')
      .optional()
      .isIn(['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics'])
      .withMessage('无效的学科分类'),
    query('status')
      .optional()
      .isIn(['pending', 'in_progress', 'completed', 'cancelled'])
      .withMessage('无效的任务状态'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码应为正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量应在1-100之间'),
    query('sortBy')
      .optional()
      .isIn(['createdAt', 'updatedAt', 'priority', 'difficulty'])
      .withMessage('无效的排序字段'),
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('排序方向应为asc或desc')
  ],
  taskController.getTaskList
);

// 获取任务详情路由
router.get('/:taskId',
  [
    param('taskId')
      .isUUID()
      .withMessage('无效的任务ID格式')
  ],
  taskController.getTaskDetail
);

// 更新任务路由
router.put('/:taskId',
  uploadTaskFiles, // 文件上传处理
  [
    param('taskId')
      .isUUID()
      .withMessage('无效的任务ID格式'),
    body('title')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('任务标题长度应在1-100字符之间'),
    body('content')
      .optional()
      .trim()
      .isLength({ min: 1, max: 5000 })
      .withMessage('任务内容长度应在1-5000字符之间'),
    body('subject')
      .optional()
      .isIn(['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics'])
      .withMessage('无效的学科分类'),
    body('estimatedTime')
      .optional()
      .isInt({ min: 1, max: 300 })
      .withMessage('预估时间应在1-300分钟之间'),
    body('priority')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('优先级应在1-5之间'),
    body('difficulty')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('难度级别应在1-5之间'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签应为数组格式')
  ],
  taskController.updateTask
);

// 删除任务路由
router.delete('/:taskId',
  [
    param('taskId')
      .isUUID()
      .withMessage('无效的任务ID格式')
  ],
  taskController.deleteTask
);

// 更新任务状态路由
router.patch('/:taskId/status',
  [
    param('taskId')
      .isUUID()
      .withMessage('无效的任务ID格式'),
    body('status')
      .isIn(['pending', 'in_progress', 'completed', 'cancelled'])
      .withMessage('无效的任务状态'),
    body('notes')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('备注长度不能超过1000字符')
  ],
  taskController.updateTaskStatus
);

// 负载均衡检查路由
router.post('/load-check',
  [
    body('targetDate')
      .isISO8601()
      .withMessage('无效的日期格式'),
    body('newTaskDuration')
      .isInt({ min: 1, max: 300 })
      .withMessage('任务时长应在1-300分钟之间'),
    body('userDailyLimit')
      .optional()
      .isInt({ min: 30, max: 600 })
      .withMessage('每日学习时间限制应在30-600分钟之间')
  ],
  async (req, res) => {
    try {
      const { targetDate, newTaskDuration } = req.body;
      const userId = req.user.id;

      const loadBalanceService = require('../services/loadBalanceService');
      const result = await loadBalanceService.calculateDailyLoad(
        new Date(targetDate),
        newTaskDuration,
        userId
      );

      res.json({
        success: true,
        data: {
          loadLevel: result.loadLevel,
          currentLoad: result.currentLoad,
          suggestions: result.suggestions,
          alternativeDates: result.alternativeDates
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '负载检查失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }
);

module.exports = router;
```

### [DEV-ROUTE-002] 认证路由
**路由ID**：DEV-ROUTE-002
**实现API**：[DES-API-009] 用户认证API

#### auth.js 认证路由实现
```javascript
// src/routes/auth.js
const express = require('express');
const { body } = require('express-validator');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const User = require('../models/User');
const { authMiddleware } = require('../middleware/auth');
const rateLimitMiddleware = require('../middleware/rateLimit');
const logger = require('../config/logger');

const router = express.Router();

// 用户注册
router.post('/register',
  rateLimitMiddleware.register,
  [
    body('username')
      .trim()
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度应在3-50字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请输入有效的邮箱地址'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6位')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('密码必须包含大小写字母和数字'),
    body('nickname')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('昵称长度不能超过50字符')
  ],
  async (req, res) => {
    try {
      const { username, email, password, nickname } = req.body;

      // 检查用户是否已存在
      const existingUser = await User.findOne({
        $or: [{ username }, { email }]
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: existingUser.username === username ? '用户名已存在' : '邮箱已被注册'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 创建新用户
      const user = new User({
        userId: uuidv4(),
        username,
        email,
        password,
        nickname: nickname || username
      });

      await user.save();

      // 生成JWT token
      const token = jwt.sign(
        { userId: user._id },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      logger.info('User registered successfully', {
        userId: user._id,
        username,
        email
      });

      res.status(201).json({
        success: true,
        data: {
          token,
          user: user.toSafeObject(),
          expiresIn: 7 * 24 * 60 * 60 // 7天（秒）
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Registration error', {
        error: error.message,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '注册失败，请重试'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }
);

// 用户登录
router.post('/login',
  rateLimitMiddleware.login,
  [
    body('username')
      .trim()
      .notEmpty()
      .withMessage('请输入用户名或邮箱'),
    body('password')
      .notEmpty()
      .withMessage('请输入密码')
  ],
  async (req, res) => {
    try {
      const { username, password } = req.body;

      // 查找用户（支持用户名或邮箱登录）
      const user = await User.findOne({
        $or: [{ username }, { email: username }],
        isActive: true
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '用户名或密码错误'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '用户名或密码错误'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      // 生成JWT token
      const token = jwt.sign(
        { userId: user._id },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      // 更新最后登录时间
      user.lastLoginAt = new Date();
      await user.save();

      logger.info('User logged in successfully', {
        userId: user._id,
        username: user.username
      });

      res.json({
        success: true,
        data: {
          token,
          user: user.toSafeObject(),
          expiresIn: 7 * 24 * 60 * 60 // 7天（秒）
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });

    } catch (error) {
      logger.error('Login error', {
        error: error.message,
        requestId: req.headers['x-request-id']
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '登录失败，请重试'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }
);

// 获取当前用户信息
router.get('/me', authMiddleware, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: '用户不存在'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }

    res.json({
      success: true,
      data: user.toSafeObject(),
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  } catch (error) {
    logger.error('Get user info error', {
      error: error.message,
      userId: req.user?.id,
      requestId: req.headers['x-request-id']
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '获取用户信息失败'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
});

// 用户登出
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    // 这里可以实现token黑名单机制
    // 目前简单返回成功响应

    logger.info('User logged out', {
      userId: req.user.id,
      username: req.user.username
    });

    res.json({
      success: true,
      data: {
        message: '登出成功'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  } catch (error) {
    logger.error('Logout error', {
      error: error.message,
      userId: req.user?.id,
      requestId: req.headers['x-request-id']
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '登出失败'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
});

module.exports = router;
```

### [DEV-ROUTE-003] 复习路由（基础版本）
**路由ID**：DEV-ROUTE-003
**实现API**：[DES-API-005] 到 [DES-API-006] 复习管理API

#### reviews.js 复习路由实现
```javascript
// src/routes/reviews.js
const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const ReviewSchedule = require('../models/ReviewSchedule');
const logger = require('../config/logger');

const router = express.Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取复习提醒
router.get('/reminders', async (req, res) => {
  try {
    const { date, upcoming } = req.query;
    const userId = req.user.id;

    let query = { userId };

    if (date) {
      const targetDate = new Date(date);
      const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
      const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));
      query.scheduledTime = { $gte: startOfDay, $lte: endOfDay };
    }

    if (upcoming === 'true') {
      query.status = { $in: ['scheduled', 'overdue'] };
    }

    const reminders = await ReviewSchedule.find(query)
      .populate('taskId', 'title')
      .sort({ scheduledTime: 1 })
      .limit(20);

    res.json({
      success: true,
      data: {
        reminders: reminders.map(review => ({
          reviewId: review._id,
          taskTitle: review.taskId?.title || '未知任务',
          reviewTime: review.scheduledTime,
          reviewIndex: review.reviewIndex,
          isOverdue: review.status === 'overdue'
        })),
        totalCount: reminders.length
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  } catch (error) {
    logger.error('Get reminders error', {
      error: error.message,
      userId: req.user?.id,
      requestId: req.headers['x-request-id']
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '获取复习提醒失败'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
});

module.exports = router;
```

### [DEV-ROUTE-004] 分析路由（基础版本）
**路由ID**：DEV-ROUTE-004
**实现API**：[DES-API-007] 分析统计API

#### analytics.js 分析路由实现
```javascript
// src/routes/analytics.js
const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const Task = require('../models/Task');
const ReviewSchedule = require('../models/ReviewSchedule');
const logger = require('../config/logger');

const router = express.Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取基础统计信息
router.get('/basic-stats', async (req, res) => {
  try {
    const userId = req.user.id;

    const [totalTasks, completedTasks, totalReviews, completedReviews] = await Promise.all([
      Task.countDocuments({ userId }),
      Task.countDocuments({ userId, status: 'completed' }),
      ReviewSchedule.countDocuments({ userId }),
      ReviewSchedule.countDocuments({ userId, status: 'completed' })
    ]);

    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    const reviewCompletionRate = totalReviews > 0 ? Math.round((completedReviews / totalReviews) * 100) : 0;

    res.json({
      success: true,
      data: {
        totalTasks,
        completedTasks,
        completionRate,
        totalReviews,
        completedReviews,
        reviewCompletionRate
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  } catch (error) {
    logger.error('Get basic stats error', {
      error: error.message,
      userId: req.user?.id,
      requestId: req.headers['x-request-id']
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '获取统计信息失败'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
});

module.exports = router;
```

## 🚀 应用入口配置

### [DEV-APP-001] Express应用配置
**应用ID**：DEV-APP-001
**基于架构**：[DES-ARCH-001] 系统整体架构设计

#### app.js 应用入口实现
```javascript
// src/app.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入配置
const connectDB = require('./config/database');
const logger = require('./config/logger');

// 导入路由
const taskRoutes = require('./routes/tasks');
const reviewRoutes = require('./routes/reviews');
const authRoutes = require('./routes/auth');
const analyticsRoutes = require('./routes/analytics');

// 导入中间件
const errorHandler = require('./middleware/errorHandler');
const { generateRequestId } = require('./middleware/requestId');

const app = express();

// 连接数据库
connectDB();

// 基础中间件
app.use(helmet()); // 安全头设置
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// 请求ID生成
app.use(generateRequestId);

// 日志中间件
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// 全局限流
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMITED',
      message: '请求频率过高，请稍后再试'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(globalLimiter);

// 解析JSON请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    }
  });
});

// API路由
app.use('/api/tasks', taskRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/analytics', analyticsRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在'
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id']
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const PORT = process.env.PORT || 3001;
const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`, {
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

module.exports = app;
```

## ⚙️ 配置文件实现

### [DEV-CONFIG-001] 数据库配置
**配置ID**：DEV-CONFIG-001
**基于设计**：[DES-TECH-003] 数据存储技术

#### database.js 数据库配置
```javascript
// src/config/database.js
const mongoose = require('mongoose');
const logger = require('./logger');

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ebbinghaus_learning';

    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10, // 最大连接池大小
      serverSelectionTimeoutMS: 5000, // 服务器选择超时
      socketTimeoutMS: 45000, // Socket超时
      bufferMaxEntries: 0, // 禁用mongoose缓冲
      bufferCommands: false // 禁用mongoose缓冲命令
    };

    const conn = await mongoose.connect(mongoURI, options);

    logger.info('MongoDB connected successfully', {
      host: conn.connection.host,
      port: conn.connection.port,
      database: conn.connection.name
    });

    // 监听连接事件
    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error', { error: err.message });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
    });

    // 优雅关闭
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    logger.error('MongoDB connection failed', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
};

module.exports = connectDB;
```

### [DEV-CONFIG-003] 日志配置
**配置ID**：DEV-CONFIG-003
**基于技术**：Winston 3.8+

#### logger.js 日志配置
```javascript
// src/config/logger.js
const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// 创建日志目录
const logDir = process.env.LOG_FILE_PATH || './logs';

// 定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// 控制台输出格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// 创建日志传输器
const transports = [
  // 控制台输出
  new winston.transports.Console({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: consoleFormat,
    handleExceptions: true,
    handleRejections: true
  }),

  // 错误日志文件
  new DailyRotateFile({
    filename: path.join(logDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    format: logFormat,
    maxSize: '20m',
    maxFiles: '14d',
    handleExceptions: true,
    handleRejections: true
  }),

  // 组合日志文件
  new DailyRotateFile({
    filename: path.join(logDir, 'combined-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    format: logFormat,
    maxSize: '20m',
    maxFiles: '30d'
  }),

  // 应用日志文件
  new DailyRotateFile({
    filename: path.join(logDir, 'app-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'info',
    format: logFormat,
    maxSize: '20m',
    maxFiles: '30d'
  })
];

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'ebbinghaus-backend',
    version: process.env.npm_package_version || '1.0.0'
  },
  transports,
  exitOnError: false
});

// 生产环境不输出到控制台
if (process.env.NODE_ENV === 'production') {
  logger.remove(logger.transports[0]); // 移除控制台传输器
}

// 添加流接口，用于Morgan HTTP日志
logger.stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

// 扩展方法：记录HTTP请求
logger.logRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    requestId: req.headers['x-request-id']
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request Error', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// 扩展方法：记录数据库操作
logger.logDatabase = (operation, collection, query, result) => {
  logger.debug('Database Operation', {
    operation,
    collection,
    query: JSON.stringify(query),
    resultCount: Array.isArray(result) ? result.length : result ? 1 : 0,
    timestamp: new Date().toISOString()
  });
};

// 扩展方法：记录业务操作
logger.logBusiness = (action, userId, data) => {
  logger.info('Business Operation', {
    action,
    userId,
    data,
    timestamp: new Date().toISOString()
  });
};

// 扩展方法：记录性能指标
logger.logPerformance = (operation, duration, metadata = {}) => {
  logger.info('Performance Metric', {
    operation,
    duration: `${duration}ms`,
    ...metadata,
    timestamp: new Date().toISOString()
  });
};

// 错误处理
logger.on('error', (error) => {
  console.error('Logger error:', error);
});

module.exports = logger;
```

## 🛡️ 中间件实现

### [DEV-MIDDLEWARE-001] 认证中间件
**中间件ID**：DEV-MIDDLEWARE-001
**功能**：JWT Token验证和用户身份认证

#### auth.js 认证中间件
```javascript
// src/middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../config/logger');

/**
 * JWT认证中间件
 */
const authMiddleware = async (req, res, next) => {
  try {
    // 获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '缺少认证令牌'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }

    const token = authHeader.substring(7); // 移除 'Bearer ' 前缀

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 查找用户
    const user = await User.findById(decoded.userId).select('-password');
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '用户不存在或已被禁用'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user._id,
      userId: user.userId,
      username: user.username,
      email: user.email,
      preferences: user.preferences
    };

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await user.save();

    next();
  } catch (error) {
    logger.error('Authentication error', {
      error: error.message,
      requestId: req.headers['x-request-id']
    });

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '无效的认证令牌'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '认证令牌已过期'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: '认证服务异常'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
};

/**
 * 可选认证中间件（用户可能未登录的接口）
 */
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // 没有token，继续执行
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');

    if (user && user.isActive) {
      req.user = {
        id: user._id,
        userId: user.userId,
        username: user.username,
        email: user.email,
        preferences: user.preferences
      };
    }

    next();
  } catch (error) {
    // 可选认证失败不阻止请求继续
    logger.warn('Optional authentication failed', {
      error: error.message,
      requestId: req.headers['x-request-id']
    });
    next();
  }
};

module.exports = {
  authMiddleware,
  optionalAuthMiddleware
};
```

### [DEV-MIDDLEWARE-002] 限流中间件
**中间件ID**：DEV-MIDDLEWARE-002
**功能**：API请求频率限制

#### rateLimit.js 限流中间件
```javascript
// src/middleware/rateLimit.js
const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');

// 创建限流器的通用函数
const createLimiter = (options) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000, // 默认15分钟
    max: options.max || 100, // 默认100个请求
    message: {
      success: false,
      error: {
        code: 'RATE_LIMITED',
        message: options.message || '请求频率过高，请稍后再试'
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        url: req.url,
        userAgent: req.get('User-Agent'),
        requestId: req.headers['x-request-id']
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMITED',
          message: options.message || '请求频率过高，请稍后再试'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    },
    skip: (req) => {
      // 跳过健康检查请求
      return req.url === '/health';
    }
  });
};

// 不同类型的限流器
const rateLimiters = {
  // 创建任务限流器
  createTask: createLimiter({
    windowMs: 1 * 60 * 1000, // 1分钟
    max: 5, // 每分钟最多5个请求
    message: '创建任务过于频繁，请稍后再试'
  }),

  // 登录限流器
  login: createLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 每15分钟最多5次登录尝试
    message: '登录尝试过于频繁，请15分钟后再试'
  }),

  // 注册限流器
  register: createLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 3, // 每小时最多3次注册
    message: '注册过于频繁，请1小时后再试'
  }),

  // 密码重置限流器
  passwordReset: createLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 3, // 每小时最多3次密码重置
    message: '密码重置请求过于频繁，请1小时后再试'
  }),

  // 一般API限流器
  general: createLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每15分钟最多100个请求
    message: '请求过于频繁，请稍后再试'
  }),

  // 严格限流器（用于敏感操作）
  strict: createLimiter({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 10, // 每5分钟最多10个请求
    message: '操作过于频繁，请稍后再试'
  })
};

module.exports = rateLimiters;
```

### [DEV-MIDDLEWARE-003] 错误处理中间件
**中间件ID**：DEV-MIDDLEWARE-003
**功能**：统一错误处理和响应格式

#### errorHandler.js 错误处理中间件
```javascript
// src/middleware/errorHandler.js
const logger = require('../config/logger');

/**
 * 统一错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  logger.error('Error occurred', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.headers['x-request-id'],
    userId: req.user?.id
  });

  // Mongoose验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = {
      code: 'VALIDATION_ERROR',
      message: `数据验证失败: ${message}`,
      statusCode: 400
    };
  }

  // Mongoose重复键错误
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    error = {
      code: 'DUPLICATE_FIELD',
      message: `${field} 已存在`,
      statusCode: 409
    };
  }

  // Mongoose转换错误
  if (err.name === 'CastError') {
    error = {
      code: 'INVALID_ID',
      message: '无效的资源ID',
      statusCode: 400
    };
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    error = {
      code: 'UNAUTHORIZED',
      message: '无效的认证令牌',
      statusCode: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      code: 'UNAUTHORIZED',
      message: '认证令牌已过期',
      statusCode: 401
    };
  }

  // 默认错误
  const statusCode = error.statusCode || err.statusCode || 500;
  const message = error.message || '服务器内部错误';
  const code = error.code || 'SERVER_ERROR';

  res.status(statusCode).json({
    success: false,
    error: {
      code,
      message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id']
  });
};

module.exports = errorHandler;
```

### [DEV-MIDDLEWARE-004] 请求ID中间件
**中间件ID**：DEV-MIDDLEWARE-004
**功能**：为每个请求生成唯一ID用于追踪

#### requestId.js 请求ID中间件
```javascript
// src/middleware/requestId.js
const { v4: uuidv4 } = require('uuid');

/**
 * 生成请求ID中间件
 */
const generateRequestId = (req, res, next) => {
  // 如果请求头中已有请求ID，使用现有的；否则生成新的
  const requestId = req.headers['x-request-id'] || uuidv4();

  // 设置请求头
  req.headers['x-request-id'] = requestId;

  // 设置响应头
  res.setHeader('X-Request-ID', requestId);

  next();
};

module.exports = {
  generateRequestId
};
```

### [DEV-MIDDLEWARE-005] 文件上传中间件
**中间件ID**：DEV-MIDDLEWARE-005
**功能**：处理多媒体文件上传

#### upload.js 文件上传中间件
```javascript
// src/middleware/upload.js
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');

// 配置存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';

    // 根据文件类型分类存储
    if (file.mimetype.startsWith('image/')) {
      uploadPath += 'images/';
    } else if (file.mimetype.startsWith('audio/')) {
      uploadPath += 'audio/';
    } else {
      uploadPath += 'attachments/';
    }

    // 确保目录存在
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.UPLOAD_ALLOWED_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/gif',
    'audio/mpeg',
    'audio/wav',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
  }
};

// 创建multer实例
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10 * 1024 * 1024, // 默认10MB
    files: 10 // 最多10个文件
  }
});

// 任务文件上传中间件
const uploadTaskFiles = upload.fields([
  { name: 'images', maxCount: 5 },
  { name: 'audio', maxCount: 1 },
  { name: 'attachments', maxCount: 5 }
]);

// 错误处理包装器
const handleUploadError = (uploadMiddleware) => {
  return (req, res, next) => {
    uploadMiddleware(req, res, (err) => {
      if (err instanceof multer.MulterError) {
        logger.error('File upload error', {
          error: err.message,
          code: err.code,
          requestId: req.headers['x-request-id']
        });

        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(413).json({
            success: false,
            error: {
              code: 'FILE_TOO_LARGE',
              message: '文件大小超出限制'
            },
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id']
          });
        }

        if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(413).json({
            success: false,
            error: {
              code: 'TOO_MANY_FILES',
              message: '文件数量超出限制'
            },
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id']
          });
        }

        return res.status(400).json({
          success: false,
          error: {
            code: 'UPLOAD_ERROR',
            message: '文件上传失败'
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      if (err) {
        logger.error('File upload error', {
          error: err.message,
          requestId: req.headers['x-request-id']
        });

        return res.status(400).json({
          success: false,
          error: {
            code: 'UPLOAD_ERROR',
            message: err.message
          },
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id']
        });
      }

      next();
    });
  };
};

module.exports = {
  uploadTaskFiles: handleUploadError(uploadTaskFiles),
  upload
};
```
```

### [DEV-CONFIG-002] 环境变量配置
**配置ID**：DEV-CONFIG-002
**配置文件**：.env

#### .env 环境变量配置
```bash
# .env
# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_learning

# Redis配置（可选）
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,audio/mpeg,audio/wav

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 腾讯云配置（可选）
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
TENCENT_REGION=ap-beijing
```

## 📦 Package.json 配置

### [DEV-PACKAGE-001] 项目依赖配置
**配置ID**：DEV-PACKAGE-001
**包管理器**：npm

#### package.json 完整配置
```json
{
  "name": "ebbinghaus-backend",
  "version": "1.0.0",
  "description": "艾宾浩斯记忆曲线学习管理系统后端服务",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/**/*.js",
    "lint:fix": "eslint src/**/*.js --fix",
    "format": "prettier --write src/**/*.js"
  },
  "keywords": [
    "ebbinghaus",
    "learning",
    "memory",
    "education",
    "nodejs",
    "express",
    "mongodb"
  ],
  "author": "Your Name",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.6.0",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "morgan": "^1.10.0",
    "jsonwebtoken": "^9.0.2",
    "bcryptjs": "^2.4.3",
    "dotenv": "^16.3.1",
    "express-rate-limit": "^7.1.0",
    "express-validator": "^7.0.1",
    "winston": "^3.11.0",
    "winston-daily-rotate-file": "^4.7.1",
    "redis": "^4.6.10",
    "ioredis": "^5.3.2",
    "dayjs": "^1.11.10",
    "uuid": "^9.0.1",
    "multer": "^1.4.5-lts.1",
    "nodemailer": "^6.9.7"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "@types/node": "^20.5.0",
    "typescript": "^5.1.6",
    "@types/express": "^4.17.17",
    "@types/jsonwebtoken": "^9.0.2",
    "@types/bcryptjs": "^2.4.2",
    "@types/cors": "^2.8.13",
    "eslint": "^8.47.0",
    "prettier": "^3.0.1",
    "jest": "^29.6.2",
    "supertest": "^6.3.3"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

## 🚀 启动和部署

### [DEV-START-001] 开发环境启动
**启动ID**：DEV-START-001
**环境类型**：本地开发环境

#### 启动步骤
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 3. 启动MongoDB（如果使用本地MongoDB）
mongod --dbpath /path/to/your/db

# 4. 启动开发服务器
npm run dev

# 5. 验证服务启动
curl http://localhost:3001/health
```

### [DEV-START-002] 生产环境部署
**部署ID**：DEV-START-002
**环境类型**：生产环境

#### 部署步骤
```bash
# 1. 克隆代码
git clone <repository-url>
cd ebbinghaus-backend

# 2. 安装生产依赖
npm ci --only=production

# 3. 配置生产环境变量
# 设置 NODE_ENV=production
# 配置生产数据库连接
# 设置强密码的JWT_SECRET

# 4. 使用PM2启动应用
npm install -g pm2
pm2 start ecosystem.config.js

# 5. 设置开机自启
pm2 startup
pm2 save
```

#### ecosystem.config.js PM2配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'ebbinghaus-backend',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

---

**文档版本**：v1.0
**创建时间**：2025-02-01
**最后更新**：2025-02-01
**负责人**：后端开发工程师
**审核人**：技术架构师
**状态**：已发布
```
