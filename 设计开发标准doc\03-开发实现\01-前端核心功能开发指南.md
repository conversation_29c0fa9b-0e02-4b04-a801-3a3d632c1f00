# [DEV-FRONTEND-001] 前端核心功能开发指南

## 📋 概述

本文档基于 [DES-ARCH-003] 前端技术架构和 [DES-UI-001] 用户界面设计规范，提供艾宾浩斯记忆曲线学习管理系统前端核心功能的详细开发指南。重点实现学习任务管理、艾宾浩斯复习计划展示、负载均衡预警等核心功能模块。

## 🎯 开发目标

### 核心功能模块
- **学习任务管理界面**：实现 [REQ-FUNC-001] 到 [REQ-FUNC-006] 的前端交互
- **艾宾浩斯复习计划展示**：基于 [DES-ALGO-001] 的可视化展示
- **负载均衡预警界面**：基于 [DES-ALGO-002] 的预警提示
- **基础用户交互组件**：符合 [TERM-021] 交互设计技术实现

### 技术实现目标
- 基于 [DES-TECH-001] 前端技术栈：Vue 3 + Element Plus + Tailwind CSS
- 遵循 [DES-UI-002] 组件设计规范
- 集成 [DES-API-001] 到 [DES-API-007] API接口
- 实现 [REQ-NFUNC-001] 响应时间要求（≤2秒页面加载）

## 🔧 开发环境配置

### [DEV-ENV-001] 基础环境要求
**环境ID**：DEV-ENV-001  
**基础要求**：
- Node.js 18+ LTS
- npm 9+ 或 yarn 1.22+
- Git 2.30+
- VS Code + Vue Language Features (Volar)

### [DEV-ENV-002] 项目初始化
**配置ID**：DEV-ENV-002  
**初始化步骤**：

```bash
# 1. 创建项目
npm create vue@latest ebbinghaus-learning-system
cd ebbinghaus-learning-system

# 2. 选择配置选项
✔ Add TypeScript? Yes
✔ Add JSX Support? No
✔ Add Vue Router for Single Page Application development? Yes
✔ Add Pinia for state management? Yes
✔ Add Vitest for Unit Testing? Yes
✔ Add an End-to-End Testing Solution? Playwright
✔ Add ESLint for code quality? Yes
✔ Add Prettier for code formatting? Yes

# 3. 安装依赖
npm install

# 4. 安装UI组件库和工具库
npm install element-plus @element-plus/icons-vue
npm install tailwindcss @tailwindcss/typography
npm install axios dayjs lodash-es
npm install cytoscape cytoscape-dagre cytoscape-cose-bilkent
npm install vuedraggable@next
npm install @types/lodash-es @types/cytoscape -D

# 5. 安装dayjs插件（用于工具函数）
npm install dayjs
```

### [DEV-ENV-003] 项目结构配置
**结构ID**：DEV-ENV-003  
**目录结构**：

```
src/
├── components/              # 组件目录
│   ├── common/             # 通用组件
│   │   ├── BaseButton.vue
│   │   ├── BaseInput.vue
│   │   ├── BaseModal.vue
│   │   ├── LoadingSpinner.vue
│   │   └── MediaUploader.vue
│   ├── business/           # 业务组件
│   │   ├── TaskCard.vue
│   │   ├── ReviewSchedule.vue
│   │   ├── LoadBalanceWarning.vue
│   │   ├── EbbinghausChart.vue
│   │   ├── NotificationCenter.vue
│   │   └── EfficiencyAnalysis.vue
│   ├── mindmap/            # 思维导图组件
│   │   ├── MindMapCanvas.vue
│   │   ├── MindMapToolbar.vue
│   │   ├── NodeEditor.vue
│   │   ├── TaskLinkDialog.vue
│   │   └── MindMapExporter.vue
│   └── layout/             # 布局组件
│       ├── AppHeader.vue
│       ├── AppSidebar.vue
│       └── AppMain.vue
├── views/                  # 页面视图
│   ├── task/              # 任务管理页面
│   │   ├── TaskList.vue
│   │   ├── TaskCreate.vue
│   │   ├── TaskEdit.vue
│   │   └── TaskDetail.vue
│   ├── review/            # 复习管理页面
│   │   ├── ReviewDashboard.vue
│   │   ├── ReviewExecution.vue
│   │   └── ReviewHistory.vue
│   ├── mindmap/           # 思维导图页面
│   │   ├── MindMapList.vue
│   │   ├── MindMapEditor.vue
│   │   └── MindMapViewer.vue
│   ├── analytics/         # 分析统计页面
│   │   ├── EfficiencyDashboard.vue
│   │   └── LearningReport.vue
│   └── dashboard/         # 仪表板页面
│       └── Dashboard.vue
├── stores/                # 状态管理
│   ├── task.ts           # 任务状态
│   ├── review.ts         # 复习状态
│   ├── user.ts           # 用户状态
│   └── app.ts            # 应用状态
├── services/              # API服务
│   ├── api/              # API接口
│   │   ├── task.ts
│   │   ├── review.ts
│   │   ├── mindmap.ts
│   │   ├── notification.ts
│   │   ├── analytics.ts
│   │   └── user.ts
│   ├── http.ts           # HTTP客户端
│   └── types.ts          # 类型定义
├── utils/                 # 工具函数
│   ├── date.ts
│   ├── validation.ts
│   └── constants.ts
├── styles/                # 样式文件
│   ├── main.scss
│   ├── variables.scss
│   └── components.scss
└── router/                # 路由配置
    └── index.ts
```

## 🏗️ 核心组件实现方案

### [DEV-COMP-001] 学习任务管理组件
**组件ID**：DEV-COMP-001  
**实现需求**：[REQ-FUNC-001] 学习任务创建功能  
**对应设计**：[DES-API-001] 创建学习任务

#### TaskCreate.vue 组件实现
```vue
<template>
  <div class="task-create">
    <el-form
      ref="taskFormRef"
      :model="taskForm"
      :rules="taskRules"
      label-width="120px"
      class="max-w-2xl mx-auto"
    >
      <!-- 基础信息 -->
      <el-form-item label="任务标题" prop="title">
        <el-input
          v-model="taskForm.title"
          placeholder="请输入任务标题（1-100字符）"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="学科分类" prop="subject">
        <el-select v-model="taskForm.subject" placeholder="请选择学科">
          <el-option
            v-for="subject in subjectOptions"
            :key="subject.value"
            :label="subject.label"
            :value="subject.value"
          />
        </el-select>
      </el-form-item>

      <!-- 任务内容 -->
      <el-form-item label="任务内容" prop="content">
        <el-input
          v-model="taskForm.content"
          type="textarea"
          :rows="6"
          placeholder="请输入任务内容（1-5000字符）"
          maxlength="5000"
          show-word-limit
        />
      </el-form-item>

      <!-- 学习参数 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="预估时间" prop="estimatedTime">
            <el-input-number
              v-model="taskForm.estimatedTime"
              :min="1"
              :max="300"
              placeholder="分钟"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="优先级" prop="priority">
            <el-rate v-model="taskForm.priority" :max="5" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="难度级别" prop="difficulty">
            <el-rate v-model="taskForm.difficulty" :max="5" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 负载预警显示 -->
      <LoadBalanceWarning
        v-if="loadWarning"
        :warning="loadWarning"
        @adjust="handleLoadAdjust"
      />

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建任务
        </el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useTaskStore } from '@/stores/task'
import { useRouter } from 'vue-router'
import LoadBalanceWarning from '@/components/business/LoadBalanceWarning.vue'
import type { TaskCreateRequest, LoadWarning } from '@/services/types'

// 状态管理
const taskStore = useTaskStore()
const router = useRouter()

// 表单引用和状态
const taskFormRef = ref<FormInstance>()
const submitting = ref(false)
const loadWarning = ref<LoadWarning | null>(null)

// 表单数据
const taskForm = reactive<TaskCreateRequest>({
  title: '',
  content: '',
  subject: '',
  estimatedTime: 30,
  priority: 3,
  difficulty: 3,
  tags: []
})

// 学科选项
const subjectOptions = [
  { label: '数学', value: 'math' },
  { label: '语文', value: 'chinese' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' }
]

// 表单验证规则
const taskRules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度应在1-100字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入任务内容', trigger: 'blur' },
    { min: 1, max: 5000, message: '内容长度应在1-5000字符之间', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择学科分类', trigger: 'change' }
  ],
  estimatedTime: [
    { required: true, message: '请输入预估时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '预估时间应在1-300分钟之间', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' },
    { type: 'number', min: 1, max: 5, message: '优先级应在1-5之间', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度级别', trigger: 'change' },
    { type: 'number', min: 1, max: 5, message: '难度级别应在1-5之间', trigger: 'change' }
  ]
}

// 提交处理
const handleSubmit = async () => {
  if (!taskFormRef.value) return
  
  try {
    await taskFormRef.value.validate()
    submitting.value = true
    
    const result = await taskStore.createTask(taskForm)
    
    if (result.loadWarning) {
      loadWarning.value = result.loadWarning
      return
    }
    
    ElMessage.success('任务创建成功')
    router.push('/tasks')
  } catch (error) {
    console.error('创建任务失败:', error)
    ElMessage.error('创建任务失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 负载调整处理
const handleLoadAdjust = (adjustment: any) => {
  // 根据调整建议修改任务参数
  if (adjustment.type === 'reschedule') {
    // 重新安排时间
    ElMessage.info('已根据建议调整任务安排')
  }
  loadWarning.value = null
}

// 重置表单
const handleReset = () => {
  taskFormRef.value?.resetFields()
  loadWarning.value = null
}
</script>
```

### [DEV-COMP-002] 艾宾浩斯复习计划组件
**组件ID**：DEV-COMP-002  
**实现需求**：[REQ-FUNC-002] 艾宾浩斯复习计划生成  
**对应设计**：[DES-ALGO-001] 艾宾浩斯记忆曲线算法

#### EbbinghausSchedule.vue 组件实现
```vue
<template>
  <div class="ebbinghaus-schedule">
    <div class="schedule-header mb-6">
      <h3 class="text-lg font-semibold text-gray-800">
        复习计划 - {{ taskTitle }}
      </h3>
      <p class="text-sm text-gray-600 mt-1">
        基于艾宾浩斯记忆曲线的科学复习安排
      </p>
    </div>

    <div class="schedule-timeline">
      <div
        v-for="(review, index) in reviewSchedule"
        :key="review.id"
        class="timeline-item"
        :class="getTimelineItemClass(review.status)"
      >
        <!-- 时间节点 -->
        <div class="timeline-marker">
          <div class="marker-circle">
            <el-icon v-if="review.status === 'completed'">
              <Check />
            </el-icon>
            <el-icon v-else-if="review.status === 'overdue'">
              <Warning />
            </el-icon>
            <span v-else class="marker-number">{{ index + 1 }}</span>
          </div>
        </div>

        <!-- 复习信息 -->
        <div class="timeline-content">
          <div class="review-card">
            <div class="card-header">
              <span class="review-index">第{{ index + 1 }}次复习</span>
              <span class="review-interval">
                {{ getIntervalText(review.intervalDays) }}
              </span>
            </div>
            
            <div class="card-body">
              <div class="review-time">
                <el-icon><Clock /></el-icon>
                <span>{{ formatDateTime(review.reviewTime) }}</span>
              </div>
              
              <div class="review-status">
                <el-tag :type="getStatusTagType(review.status)">
                  {{ getStatusText(review.status) }}
                </el-tag>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions" v-if="canOperate(review)">
              <el-button
                v-if="review.status === 'scheduled'"
                type="primary"
                size="small"
                @click="startReview(review)"
              >
                开始复习
              </el-button>
              
              <el-button
                v-if="review.status === 'in_progress'"
                type="success"
                size="small"
                @click="completeReview(review)"
              >
                完成复习
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复习执行对话框 -->
    <ReviewExecutionDialog
      v-model:visible="reviewDialogVisible"
      :review="currentReview"
      @complete="handleReviewComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Warning, Clock } from '@element-plus/icons-vue'
import { useReviewStore } from '@/stores/review'
import ReviewExecutionDialog from './ReviewExecutionDialog.vue'
import { formatDateTime } from '@/utils/date'
import type { ReviewScheduleItem } from '@/services/types'

interface Props {
  taskId: string
  taskTitle: string
  reviewSchedule: ReviewScheduleItem[]
}

const props = defineProps<Props>()
const reviewStore = useReviewStore()

// 对话框状态
const reviewDialogVisible = ref(false)
const currentReview = ref<ReviewScheduleItem | null>(null)

// 艾宾浩斯间隔文本映射
const intervalTexts = {
  0: '立即',
  0.003: '5分钟后',
  0.021: '30分钟后', 
  0.5: '12小时后',
  1: '1天后',
  3: '3天后',
  7: '1周后',
  14: '2周后',
  30: '1月后',
  60: '2月后'
}

// 获取间隔文本
const getIntervalText = (days: number): string => {
  return intervalTexts[days] || `${days}天后`
}

// 获取时间线项目样式
const getTimelineItemClass = (status: string): string => {
  const baseClass = 'flex mb-6'
  switch (status) {
    case 'completed':
      return `${baseClass} completed`
    case 'overdue':
      return `${baseClass} overdue`
    case 'in_progress':
      return `${baseClass} in-progress`
    default:
      return `${baseClass} scheduled`
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'overdue':
      return 'danger'
    case 'in_progress':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'overdue':
      return '已逾期'
    case 'in_progress':
      return '进行中'
    case 'scheduled':
      return '已安排'
    default:
      return '未知'
  }
}

// 判断是否可以操作
const canOperate = (review: ReviewScheduleItem): boolean => {
  return ['scheduled', 'in_progress'].includes(review.status)
}

// 开始复习
const startReview = async (review: ReviewScheduleItem) => {
  try {
    await reviewStore.startReview(review.id)
    currentReview.value = review
    reviewDialogVisible.value = true
  } catch (error) {
    ElMessage.error('开始复习失败')
  }
}

// 完成复习
const completeReview = (review: ReviewScheduleItem) => {
  currentReview.value = review
  reviewDialogVisible.value = true
}

// 处理复习完成
const handleReviewComplete = async (reviewData: any) => {
  try {
    await reviewStore.completeReview(reviewData)
    ElMessage.success('复习完成')
    reviewDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存复习记录失败')
  }
}
</script>

<style scoped lang="scss">
.ebbinghaus-schedule {
  .schedule-timeline {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      left: 20px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #e5e7eb;
    }
  }

  .timeline-item {
    position: relative;
    
    .timeline-marker {
      position: relative;
      z-index: 2;
      
      .marker-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f3f4f6;
        border: 2px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        color: #6b7280;
      }
    }
    
    .timeline-content {
      flex: 1;
      margin-left: 20px;
      
      .review-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .review-index {
            font-weight: 600;
            color: #374151;
          }
          
          .review-interval {
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 8px;
            border-radius: 4px;
          }
        }
        
        .card-body {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .review-time {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            font-size: 14px;
          }
        }
        
        .card-actions {
          text-align: right;
        }
      }
    }
    
    // 状态样式
    &.completed {
      .marker-circle {
        background: #10b981;
        border-color: #10b981;
        color: white;
      }
    }
    
    &.overdue {
      .marker-circle {
        background: #ef4444;
        border-color: #ef4444;
        color: white;
      }
    }
    
    &.in-progress {
      .marker-circle {
        background: #f59e0b;
        border-color: #f59e0b;
        color: white;
      }
    }
  }
}
</style>
```

## 📊 数据模型定义

### [DEV-MODEL-001] 核心数据类型定义
**模型ID**：DEV-MODEL-001
**基于设计**：[DES-MODEL-001] 到 [DES-MODEL-005] 数据模型设计

#### types.ts 完整类型定义
```typescript
// src/services/types.ts

// ==================== 基础类型 ====================

export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// ==================== 任务相关类型 ====================

export interface Task extends BaseEntity {
  taskId: string
  title: string
  content: {
    text: string
    images: string[]
    audio: string
    attachments: string[]
  }
  metadata: {
    subject: string
    estimatedTime: number
    actualTime?: number
    priority: number
    difficulty: number
    tags: string[]
  }
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  progress: {
    completedAt?: string
    startedAt?: string
    notes?: string
  }

  // 学习统计（虚拟字段）
  completionRate?: number
  reviewCount?: number
  lastReviewAt?: string

  // 关联信息
  mindMapId?: string
  parentTaskId?: string
  childTaskIds?: string[]

  // 用户信息
  userId: string
}

export interface MediaFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  uploadedAt: string
}

export interface TaskCreateRequest {
  title: string
  content: string // 前端表单中的文本内容
  subject: string
  estimatedTime: number
  priority: number
  difficulty: number
  tags: string[]
  images?: File[]
  audio?: File[]
  attachments?: File[]
  mindMapId?: string
  parentTaskId?: string
}

export interface TaskUpdateRequest extends Partial<TaskCreateRequest> {
  status?: Task['status']
}

export interface TaskListParams extends PaginationParams {
  status?: Task['status']
  subject?: string
  priority?: number
  difficulty?: number
  tags?: string[]
  search?: string
  dateFrom?: string
  dateTo?: string
}

export interface TaskListResponse {
  tasks: Task[]
  pagination: PaginationResponse
}

export interface TaskDetailResponse {
  task: Task
  reviewSchedule?: ReviewScheduleItem[]
  loadWarning?: LoadWarning
  relatedTasks?: Task[]
  mindMap?: MindMapData
}

// ==================== 复习相关类型 ====================

export interface ReviewScheduleItem extends BaseEntity {
  taskId: string
  reviewIndex: number
  intervalDays: number
  reviewTime: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'skipped' | 'overdue'
  actualReviewTime?: string
  reviewDuration?: number
  effectiveness?: number
  notes?: string
  nextReviewTime?: string
}

export interface ReviewStartRequest {
  startTime: string
  environment?: string
  mood?: number
}

export interface ReviewCompleteRequest {
  endTime: string
  effectiveness: number
  notes?: string
  difficulty?: number
  confidence?: number
  needsMoreReview?: boolean
}

export interface ReviewListParams extends PaginationParams {
  status?: ReviewScheduleItem['status']
  taskId?: string
  dateFrom?: string
  dateTo?: string
  overdue?: boolean
}

export interface ReviewListResponse {
  reviews: ReviewScheduleItem[]
  pagination: PaginationResponse
  statistics?: {
    totalReviews: number
    completedReviews: number
    overdueReviews: number
    averageEffectiveness: number
  }
}

export interface ReviewDetailResponse {
  review: ReviewScheduleItem
  task: Task
  previousReviews?: ReviewScheduleItem[]
  suggestions?: string[]
}

// ==================== 思维导图相关类型 ====================

export interface MindMapNode {
  id: string
  label: string
  type: 'root' | 'child' | 'leaf'
  content?: string
  taskId?: string
  style?: {
    backgroundColor?: string
    borderColor?: string
    textColor?: string
    fontSize?: number
    shape?: string
  }
  position: {
    x: number
    y: number
  }
  metadata?: {
    createdAt: string
    updatedAt: string
    tags?: string[]
  }
}

export interface MindMapEdge {
  id: string
  source: string
  target: string
  label?: string
  style?: {
    lineColor?: string
    lineWidth?: number
    lineStyle?: 'solid' | 'dashed' | 'dotted'
  }
}

export interface MindMapData {
  nodes: MindMapNode[]
  edges: MindMapEdge[]
  metadata?: {
    version: string
    layout?: string
    theme?: string
  }
}

export interface MindMap extends BaseEntity {
  title: string
  description?: string
  data: MindMapData
  isPublic: boolean
  tags: string[]
  userId: string

  // 统计信息
  nodeCount: number
  taskCount: number
  lastEditedAt: string

  // 协作信息
  collaborators?: string[]
  permissions?: {
    canEdit: boolean
    canView: boolean
    canShare: boolean
  }
}

export interface MindMapCreateRequest {
  title: string
  description?: string
  data: MindMapData
  isPublic?: boolean
  tags?: string[]
}

export interface MindMapUpdateRequest extends Partial<MindMapCreateRequest> {}

export interface MindMapListParams extends PaginationParams {
  search?: string
  tags?: string[]
  isPublic?: boolean
  userId?: string
}

export interface MindMapListResponse {
  mindMaps: MindMap[]
  pagination: PaginationResponse
}

// ==================== 负载均衡相关类型 ====================

export interface LoadWarning {
  level: 'light' | 'medium' | 'heavy'
  currentLoad: number
  threshold: number
  message: string
  suggestions: string[]
  alternativeDates?: string[]
  affectedTasks?: string[]
  estimatedImpact?: {
    delayDays: number
    efficiencyDrop: number
  }
}

export interface LoadBalanceData {
  date: string
  totalTime: number
  scheduledTime: number
  availableTime: number
  loadPercentage: number
  tasks: {
    taskId: string
    estimatedTime: number
    priority: number
  }[]
}

// ==================== 通知相关类型 ====================

export interface Notification extends BaseEntity {
  type: 'review_reminder' | 'task_deadline' | 'load_warning' | 'achievement' | 'system'
  title: string
  message: string
  data?: any
  isRead: boolean
  userId: string
  scheduledAt?: string
  expiresAt?: string
}

export interface NotificationSettings {
  reviewReminders: boolean
  taskDeadlines: boolean
  loadWarnings: boolean
  achievements: boolean
  systemNotifications: boolean

  // 提醒时间设置
  reminderTimes: number[] // 分钟数组，如 [5, 15, 30] 表示提前5分钟、15分钟、30分钟提醒
  quietHours: {
    enabled: boolean
    startTime: string // HH:mm 格式
    endTime: string   // HH:mm 格式
  }

  // 通知方式
  browserNotifications: boolean
  emailNotifications: boolean
  inAppNotifications: boolean
}

// ==================== 学习分析相关类型 ====================

export interface LearningRecord extends BaseEntity {
  taskId: string
  reviewId?: string
  activityType: 'study' | 'review' | 'practice' | 'test'
  startTime: string
  endTime: string
  duration: number
  effectiveness?: number
  notes?: string
  environment?: string
  mood?: number
  interruptions?: number

  // 学习数据
  focusTime?: number
  breakTime?: number
  distractionCount?: number

  userId: string
}

export interface EfficiencyMetrics {
  period: 'day' | 'week' | 'month' | 'year'
  startDate: string
  endDate: string

  // 时间统计
  totalStudyTime: number
  totalReviewTime: number
  averageSessionDuration: number

  // 效率指标
  averageEffectiveness: number
  completionRate: number
  onTimeRate: number

  // 趋势数据
  dailyStats: {
    date: string
    studyTime: number
    reviewTime: number
    effectiveness: number
    taskCount: number
  }[]

  // 学科分布
  subjectStats: {
    subject: string
    studyTime: number
    taskCount: number
    averageEffectiveness: number
  }[]

  // 改进建议
  suggestions: string[]
  strengths: string[]
  weaknesses: string[]
}

// ==================== 用户相关类型 ====================

export interface User extends BaseEntity {
  username: string
  email: string
  nickname?: string
  avatar?: string

  // 学习偏好
  preferences: {
    defaultSubject?: string
    defaultEstimatedTime: number
    defaultPriority: number
    defaultDifficulty: number

    // 复习偏好
    reviewPreferences: {
      preferredTimes: string[] // HH:mm 格式
      maxDailyReviews: number
      reviewInterval: 'strict' | 'flexible'
    }

    // 界面偏好
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
  }

  // 统计信息
  statistics: {
    totalTasks: number
    completedTasks: number
    totalStudyTime: number
    totalReviewTime: number
    averageEffectiveness: number
    currentStreak: number
    longestStreak: number
    joinedAt: string
  }

  // 通知设置
  notificationSettings: NotificationSettings
}

export interface UserLoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

export interface UserRegisterRequest {
  username: string
  email: string
  password: string
  nickname?: string
}

export interface UserUpdateRequest {
  nickname?: string
  avatar?: File
  preferences?: Partial<User['preferences']>
  notificationSettings?: Partial<NotificationSettings>
}

// ==================== API响应类型 ====================

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  requestId: string
  pagination?: PaginationResponse
}

export interface ApiError {
  code: string
  message: string
  details?: any
  timestamp: string
  requestId: string
}

// ==================== 表单验证类型 ====================

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

export interface FormValidationRules {
  [key: string]: ValidationRule[]
}

// ==================== 导出所有类型 ====================

export type {
  // 基础类型
  BaseEntity,
  PaginationParams,
  PaginationResponse,

  // 任务类型
  Task,
  MediaFile,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskListParams,
  TaskListResponse,
  TaskDetailResponse,

  // 复习类型
  ReviewScheduleItem,
  ReviewStartRequest,
  ReviewCompleteRequest,
  ReviewListParams,
  ReviewListResponse,
  ReviewDetailResponse,

  // 思维导图类型
  MindMapNode,
  MindMapEdge,
  MindMapData,
  MindMap,
  MindMapCreateRequest,
  MindMapUpdateRequest,
  MindMapListParams,
  MindMapListResponse,

  // 负载均衡类型
  LoadWarning,
  LoadBalanceData,

  // 通知类型
  Notification,
  NotificationSettings,

  // 学习分析类型
  LearningRecord,
  EfficiencyMetrics,

  // 用户类型
  User,
  UserLoginRequest,
  UserRegisterRequest,
  UserUpdateRequest,

  // API类型
  ApiResponse,
  ApiError,

  // 验证类型
  ValidationRule,
  FormValidationRules
}
```

## 📡 API集成方案

### [DEV-API-001] HTTP客户端配置
**配置ID**：DEV-API-001  
**基于设计**：[DES-API-001] 到 [DES-API-007] API接口设计

#### http.ts 配置实现
```typescript
// src/services/http.ts
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 注意：ApiResponse接口已在types.ts中定义，这里直接使用
import type { ApiResponse } from '@/services/types'

// HTTP拦截器处理后的响应格式（组件实际使用的格式）
interface ProcessedApiResponse<T = any> {
  data: T  // 直接是业务数据
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  success: boolean
  timestamp: string
  requestId: string
}

// 扩展AxiosRequestConfig以支持错误处理配置
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  // 是否显示错误提示，默认true
  showErrorMessage?: boolean
  // 是否显示成功提示，默认false
  showSuccessMessage?: boolean
  // 自定义错误处理函数
  customErrorHandler?: (error: any) => void
}

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    const token = userStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data, config } = response
    const customConfig = config as CustomAxiosRequestConfig

    if (data.success) {
      // 显示成功提示（如果配置了）
      if (customConfig.showSuccessMessage && data.data?.message) {
        ElMessage.success(data.data.message)
      }

      // 返回ProcessedApiResponse格式，简化组件中的数据访问
      return {
        data: data.data, // 将 ApiResponse.data 提升为响应的 data
        pagination: data.pagination, // 保留分页信息
        success: data.success,
        timestamp: data.timestamp,
        requestId: data.requestId
      } as AxiosResponse<ProcessedApiResponse<any>>
    } else {
      // API业务错误
      const errorMessage = data.error?.message || '请求失败'

      // 检查是否需要显示错误提示
      const shouldShowError = customConfig.showErrorMessage !== false

      if (shouldShowError) {
        ElMessage.error(errorMessage)
      }

      // 如果有自定义错误处理函数，调用它
      if (customConfig.customErrorHandler) {
        customConfig.customErrorHandler(data.error)
      }

      return Promise.reject(new Error(errorMessage))
    }
  },
  (error) => {
    const config = error.config as CustomAxiosRequestConfig
    const shouldShowError = config?.showErrorMessage !== false

    // HTTP错误处理
    if (error.response) {
      const { status, data } = error.response
      let errorMessage = ''

      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录'
          // 清除token并跳转到登录页
          const userStore = useUserStore()
          userStore.logout()
          break
        case 403:
          errorMessage = '没有权限访问该资源'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.error?.message || '网络请求失败'
      }

      if (shouldShowError) {
        ElMessage.error(errorMessage)
      }

      // 如果有自定义错误处理函数，调用它
      if (config?.customErrorHandler) {
        config.customErrorHandler(error)
      }

    } else if (error.request) {
      const errorMessage = '网络连接失败，请检查网络'
      if (shouldShowError) {
        ElMessage.error(errorMessage)
      }
    } else {
      const errorMessage = '请求配置错误'
      if (shouldShowError) {
        ElMessage.error(errorMessage)
      }
    }

    return Promise.reject(error)
  }
)

export default http
export type { ApiResponse, ProcessedApiResponse, CustomAxiosRequestConfig }

// 使用示例：修复后的数据访问方式
/*
// 1. 默认行为：直接获取业务数据
const response = await http.get('/api/tasks')
console.log(response.data) // 直接是业务数据，不需要 response.data.data

// 2. 禁用错误提示，在组件中自定义处理
const response = await http.get('/api/tasks', {
  showErrorMessage: false
})

// 3. 在组件中的正确使用方式（修复后）
const loadTasks = async () => {
  try {
    loading.value = true
    // HTTP拦截器已经处理了数据提取，直接使用 response.data
    const response = await TaskApi.getTasks()
    tasks.value = response.data // ✅ 正确：直接是业务数据

    // 分页信息也可以直接访问
    total.value = response.pagination?.total || 0
  } catch (error) {
    // 这里不需要再显示错误提示，因为拦截器已经处理了
    // 只处理业务逻辑，如重置状态等
    tasks.value = []
  } finally {
    loading.value = false
  }
}

// 4. API服务类的返回值说明
// 修复前：response.data 是 ApiResponse<T>，需要 response.data.data 获取业务数据
// 修复后：response.data 直接是业务数据 T，简化了访问方式
*/

### [DEV-API-002] 任务管理API服务
**服务ID**：DEV-API-002
**对应接口**：[DES-API-001] 创建学习任务

#### task.ts API服务实现
```typescript
// src/services/api/task.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskListResponse,
  TaskDetailResponse,
  TaskListParams
} from '../types'

export class TaskApi {
  // 创建任务
  static async createTask(data: TaskCreateRequest): Promise<ProcessedApiResponse<TaskDetailResponse>> {
    let requestData: any = data

    // 检查是否有文件需要上传
    const hasFiles = data.images?.length || data.audio?.length || data.attachments?.length

    if (hasFiles) {
      // 转换为FormData
      const formData = new FormData()

      // 添加文本字段
      Object.keys(data).forEach(key => {
        if (!['images', 'audio', 'attachments'].includes(key) && data[key] !== undefined) {
          formData.append(key, String(data[key]))
        }
      })

      // 添加文件
      data.images?.forEach(file => formData.append('images', file))
      data.audio?.forEach(file => formData.append('audio', file))
      data.attachments?.forEach(file => formData.append('attachments', file))

      requestData = formData
    }

    const response = await http.post('/tasks', requestData)
    return response.data
  }

  // 获取任务列表
  static async getTaskList(params: TaskListParams): Promise<ProcessedApiResponse<TaskListResponse>> {
    const response = await http.get('/tasks', { params })
    return response.data
  }

  // 获取任务详情
  static async getTaskDetail(taskId: string): Promise<ProcessedApiResponse<TaskDetailResponse>> {
    const response = await http.get(`/tasks/${taskId}`)
    return response.data
  }

  // 更新任务
  static async updateTask(taskId: string, data: TaskUpdateRequest): Promise<ProcessedApiResponse<TaskDetailResponse>> {
    const response = await http.put(`/tasks/${taskId}`, data)
    return response.data
  }

  // 删除任务
  static async deleteTask(taskId: string): Promise<ProcessedApiResponse<void>> {
    const response = await http.delete(`/tasks/${taskId}`)
    return response.data
  }

  // 更新任务状态
  static async updateTaskStatus(taskId: string, status: string): Promise<ProcessedApiResponse<TaskDetailResponse>> {
    const response = await http.patch(`/tasks/${taskId}/status`, { status })
    return response.data
  }
}
```

### [DEV-API-003] 复习管理API服务
**服务ID**：DEV-API-003
**对应接口**：[DES-API-002] 复习管理API

#### review.ts API服务实现
```typescript
// src/services/api/review.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  ReviewListResponse,
  ReviewDetailResponse,
  ReviewStartRequest,
  ReviewCompleteRequest,
  ReviewListParams
} from '../types'

export class ReviewApi {
  // 获取复习列表
  static async getReviewList(params: ReviewListParams): Promise<ProcessedApiResponse<ReviewListResponse>> {
    const response = await http.get('/reviews', { params })
    return response.data
  }

  // 获取即将到来的复习
  static async getUpcomingReviews(): Promise<ProcessedApiResponse<ReviewListResponse>> {
    const response = await http.get('/reviews/upcoming')
    return response.data
  }

  // 开始复习
  static async startReview(reviewId: string, data: ReviewStartRequest): Promise<ProcessedApiResponse<ReviewDetailResponse>> {
    const response = await http.post(`/reviews/${reviewId}/start`, data)
    return response.data
  }

  // 完成复习
  static async completeReview(reviewId: string, data: ReviewCompleteRequest): Promise<ProcessedApiResponse<ReviewDetailResponse>> {
    const response = await http.post(`/reviews/${reviewId}/complete`, data)
    return response.data
  }

  // 跳过复习
  static async skipReview(reviewId: string, reason: string): Promise<ProcessedApiResponse<ReviewDetailResponse>> {
    const response = await http.post(`/reviews/${reviewId}/skip`, { reason })
    return response.data
  }
}
```

### [DEV-API-004] 思维导图API服务
**服务ID**：DEV-API-004
**对应接口**：[DES-API-005] 思维导图API

#### mindmap.ts API服务实现
```typescript
// src/services/api/mindmap.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  MindMap,
  MindMapCreateRequest,
  MindMapUpdateRequest,
  MindMapListParams,
  MindMapListResponse,
  MindMapData
} from '../types'

export class MindMapApi {
  // 创建思维导图
  static async createMindMap(data: MindMapCreateRequest): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.post('/mindmaps', data)
    return response.data
  }

  // 获取思维导图列表
  static async getMindMapList(params: MindMapListParams): Promise<ProcessedApiResponse<MindMapListResponse>> {
    const response = await http.get('/mindmaps', { params })
    return response.data
  }

  // 获取思维导图详情
  static async getMindMapDetail(mindMapId: string): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.get(`/mindmaps/${mindMapId}`)
    return response.data
  }

  // 更新思维导图
  static async updateMindMap(mindMapId: string, data: MindMapUpdateRequest): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.put(`/mindmaps/${mindMapId}`, data)
    return response.data
  }

  // 删除思维导图
  static async deleteMindMap(mindMapId: string): Promise<ProcessedApiResponse<void>> {
    const response = await http.delete(`/mindmaps/${mindMapId}`)
    return response.data
  }

  // 关联任务到节点
  static async linkTaskToNode(mindMapId: string, nodeId: string, taskId: string): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.post(`/mindmaps/${mindMapId}/nodes/${nodeId}/link-task`, { taskId })
    return response.data
  }

  // 取消任务关联
  static async unlinkTaskFromNode(mindMapId: string, nodeId: string): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.delete(`/mindmaps/${mindMapId}/nodes/${nodeId}/link-task`)
    return response.data
  }

  // 导出思维导图
  static async exportMindMap(mindMapId: string, format: 'json' | 'png' | 'svg'): Promise<ProcessedApiResponse<{ url: string }>> {
    const response = await http.post(`/mindmaps/${mindMapId}/export`, { format })
    return response.data
  }

  // 复制思维导图
  static async duplicateMindMap(mindMapId: string, title?: string): Promise<ProcessedApiResponse<MindMap>> {
    const response = await http.post(`/mindmaps/${mindMapId}/duplicate`, { title })
    return response.data
  }
}
```

### [DEV-API-005] 通知管理API服务
**服务ID**：DEV-API-005
**对应接口**：[DES-API-003] 提醒机制API

#### notification.ts API服务实现
```typescript
// src/services/api/notification.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  Notification as AppNotification,
  NotificationSettings,
  PaginationParams
} from '../types'

export class NotificationApi {
  // 获取通知列表
  static async getNotifications(params: PaginationParams & {
    isRead?: boolean
    type?: AppNotification['type']
  }): Promise<ProcessedApiResponse<{ notifications: AppNotification[], pagination: any }>> {
    const response = await http.get('/notifications', { params })
    return response.data
  }

  // 标记通知为已读
  static async markAsRead(notificationId: string): Promise<ProcessedApiResponse<void>> {
    const response = await http.patch(`/notifications/${notificationId}/read`)
    return response.data
  }

  // 批量标记为已读
  static async markAllAsRead(): Promise<ProcessedApiResponse<void>> {
    const response = await http.patch('/notifications/read-all')
    return response.data
  }

  // 删除通知
  static async deleteNotification(notificationId: string): Promise<ProcessedApiResponse<void>> {
    const response = await http.delete(`/notifications/${notificationId}`)
    return response.data
  }

  // 获取通知设置
  static async getNotificationSettings(): Promise<ProcessedApiResponse<NotificationSettings>> {
    const response = await http.get('/notifications/settings')
    return response.data
  }

  // 更新通知设置
  static async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<ProcessedApiResponse<NotificationSettings>> {
    const response = await http.put('/notifications/settings', settings)
    return response.data
  }

  // 测试通知
  static async testNotification(type: AppNotification['type']): Promise<ProcessedApiResponse<void>> {
    const response = await http.post('/notifications/test', { type })
    return response.data
  }

  // 请求浏览器通知权限
  static async requestBrowserPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  // 发送浏览器通知
  static sendBrowserNotification(title: string, options?: NotificationOptions): Notification | null {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return null
    }

    return new Notification(title, {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      ...options
    })
  }
}
```

### [DEV-API-006] 学习分析API服务
**服务ID**：DEV-API-006
**对应接口**：[DES-API-004] 时间管理API

#### analytics.ts API服务实现
```typescript
// src/services/api/analytics.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  EfficiencyMetrics,
  LearningRecord,
  LoadBalanceData,
  PaginationParams
} from '../types'

export class AnalyticsApi {
  // 获取学习效率分析
  static async getEfficiencyMetrics(params: {
    period: 'day' | 'week' | 'month' | 'year'
    startDate?: string
    endDate?: string
  }): Promise<ProcessedApiResponse<EfficiencyMetrics>> {
    const response = await http.get('/analytics/efficiency', { params })
    return response.data
  }

  // 获取学习记录
  static async getLearningRecords(params: PaginationParams & {
    taskId?: string
    activityType?: LearningRecord['activityType']
    startDate?: string
    endDate?: string
  }): Promise<ProcessedApiResponse<{ records: LearningRecord[], pagination: any }>> {
    const response = await http.get('/analytics/records', { params })
    return response.data
  }

  // 创建学习记录
  static async createLearningRecord(data: Omit<LearningRecord, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): Promise<ProcessedApiResponse<LearningRecord>> {
    const response = await http.post('/analytics/records', data)
    return response.data
  }

  // 获取负载均衡数据
  static async getLoadBalanceData(params: {
    startDate: string
    endDate: string
  }): Promise<ProcessedApiResponse<LoadBalanceData[]>> {
    const response = await http.get('/analytics/load-balance', { params })
    return response.data
  }

  // 获取时间预估准确性
  static async getTimeEstimationAccuracy(): Promise<ProcessedApiResponse<{
    averageAccuracy: number
    improvementSuggestions: string[]
    accuracyTrend: { date: string; accuracy: number }[]
  }>> {
    const response = await http.get('/analytics/time-estimation')
    return response.data
  }

  // 获取学习习惯分析
  static async getLearningHabits(): Promise<ProcessedApiResponse<{
    preferredTimes: { hour: number; frequency: number }[]
    averageSessionDuration: number
    mostProductiveHours: number[]
    suggestions: string[]
  }>> {
    const response = await http.get('/analytics/habits')
    return response.data
  }

  // 获取学科表现分析
  static async getSubjectPerformance(): Promise<ProcessedApiResponse<{
    subjects: {
      subject: string
      averageEffectiveness: number
      totalTime: number
      taskCount: number
      improvementRate: number
    }[]
    recommendations: string[]
  }>> {
    const response = await http.get('/analytics/subjects')
    return response.data
  }
}
```

### [DEV-API-007] 用户管理API服务
**服务ID**：DEV-API-007
**对应接口**：[DES-API-001] 用户管理API

#### user.ts API服务实现
```typescript
// src/services/api/user.ts
import http, { type ProcessedApiResponse } from '../http'
import type {
  User,
  UserLoginRequest,
  UserRegisterRequest,
  UserUpdateRequest,
  UserLoginResponse
} from '../types'

export class UserApi {
  // 用户登录
  static async login(credentials: UserLoginRequest): Promise<ProcessedApiResponse<UserLoginResponse>> {
    const response = await http.post('/auth/login', credentials)
    return response.data
  }

  // 用户注册
  static async register(userData: UserRegisterRequest): Promise<ProcessedApiResponse<UserLoginResponse>> {
    const response = await http.post('/auth/register', userData)
    return response.data
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<ProcessedApiResponse<User>> {
    const response = await http.get('/auth/me')
    return response.data
  }

  // 更新用户信息
  static async updateUser(userData: UserUpdateRequest): Promise<ProcessedApiResponse<User>> {
    const response = await http.put('/auth/me', userData)
    return response.data
  }

  // 验证Token
  static async validateToken(): Promise<ProcessedApiResponse<{ valid: boolean }>> {
    const response = await http.get('/auth/validate')
    return response.data
  }

  // 用户登出
  static async logout(): Promise<ProcessedApiResponse<void>> {
    const response = await http.post('/auth/logout')
    return response.data
  }

  // 刷新Token
  static async refreshToken(): Promise<ProcessedApiResponse<{ token: string }>> {
    const response = await http.post('/auth/refresh')
    return response.data
  }
}

export { TaskApi }
```

### [DEV-API-002] 用户API服务
**服务ID**：DEV-API-002
**实现API**：[DES-API-009] 用户认证API

#### user.ts API服务实现
```typescript
// src/services/api/user.ts
import { http } from '../http'
import type {
  User,
  UserLoginRequest,
  UserRegisterRequest,
  UserUpdateRequest,
  ApiResponse
} from '../types'

export class UserApi {
  /**
   * 用户注册
   */
  static async register(data: UserRegisterRequest): Promise<ApiResponse<{ token: string; user: User; expiresIn: number }>> {
    return http.post('/auth/register', data)
  }

  /**
   * 用户登录
   */
  static async login(data: UserLoginRequest): Promise<ApiResponse<{ token: string; user: User; expiresIn: number }>> {
    return http.post('/auth/login', data)
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    return http.get('/auth/me')
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<{ message: string }>> {
    return http.post('/auth/logout')
  }

  /**
   * 更新用户信息
   */
  static async updateUser(data: UserUpdateRequest): Promise<ApiResponse<User>> {
    return http.put('/auth/me', data)
  }
}

export { UserApi }
```

### [DEV-API-003] 通知API服务
**服务ID**：DEV-API-003
**实现API**：基础通知功能

#### notification.ts API服务实现
```typescript
// src/services/api/notification.ts
import { http } from '../http'
import type {
  Notification as AppNotification,
  NotificationSettings,
  ApiResponse
} from '../types'

export class NotificationApi {
  /**
   * 获取通知列表
   */
  static async getNotifications(params?: {
    page?: number
    limit?: number
    unreadOnly?: boolean
  }): Promise<ApiResponse<{
    notifications: AppNotification[]
    totalCount: number
    unreadCount: number
  }>> {
    return http.get('/notifications', { params })
  }

  /**
   * 标记通知为已读
   */
  static async markAsRead(notificationId: string): Promise<ApiResponse<void>> {
    return http.patch(`/notifications/${notificationId}/read`)
  }

  /**
   * 标记所有通知为已读
   */
  static async markAllAsRead(): Promise<ApiResponse<void>> {
    return http.patch('/notifications/read-all')
  }

  /**
   * 删除通知
   */
  static async deleteNotification(notificationId: string): Promise<ApiResponse<void>> {
    return http.delete(`/notifications/${notificationId}`)
  }

  /**
   * 获取通知设置
   */
  static async getSettings(): Promise<ApiResponse<NotificationSettings>> {
    return http.get('/notifications/settings')
  }

  /**
   * 更新通知设置
   */
  static async updateSettings(settings: NotificationSettings): Promise<ApiResponse<NotificationSettings>> {
    return http.put('/notifications/settings', settings)
  }
}

export { NotificationApi }
```

## 🏪 状态管理方案

### [DEV-STORE-001] 任务状态管理
**状态ID**：DEV-STORE-001
**基于设计**：[DES-UI-003] 状态管理设计

#### task.ts 状态管理实现
```typescript
// src/stores/task.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { TaskApi } from '@/services/api/task'
import type {
  Task,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskListParams,
  LoadWarning
} from '@/services/types'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const tasksBySubject = computed(() => {
    const grouped: Record<string, Task[]> = {}
    tasks.value.forEach(task => {
      if (!grouped[task.metadata.subject]) {
        grouped[task.metadata.subject] = []
      }
      grouped[task.metadata.subject].push(task)
    })
    return grouped
  })

  const pendingTasks = computed(() =>
    tasks.value.filter(task => task.status === 'pending')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'completed')
  )

  // 操作方法
  const fetchTasks = async (params: TaskListParams = {}) => {
    try {
      loading.value = true
      const response = await TaskApi.getTaskList({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      })

      tasks.value = response.data?.tasks || []
      total.value = response.pagination?.total || 0
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: TaskCreateRequest) => {
    try {
      loading.value = true
      const response = await TaskApi.createTask(taskData)

      if (response.data) {
        // 修复：根据ProcessedApiResponse<TaskDetailResponse>类型
        // response.data 现在直接是 TaskDetailResponse
        const taskDetail = response.data  // TaskDetailResponse
        const task = taskDetail.task      // Task
        tasks.value.unshift(task)
        total.value += 1

        // 直接返回TaskDetailResponse，包含组件期望的所有数据
        return taskDetail
      }
    } catch (error) {
      console.error('创建任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (taskId: string, taskData: TaskUpdateRequest) => {
    try {
      loading.value = true
      const response = await TaskApi.updateTask(taskId, taskData)

      if (response.data) {
        const index = tasks.value.findIndex(task => task.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data.task
        }

        if (currentTask.value?.taskId === taskId) {
          currentTask.value = response.data.task
        }
      }
    } catch (error) {
      console.error('更新任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (taskId: string) => {
    try {
      loading.value = true
      await TaskApi.deleteTask(taskId)

      tasks.value = tasks.value.filter(task => task.taskId !== taskId)
      total.value -= 1

      if (currentTask.value?.taskId === taskId) {
        currentTask.value = null
      }
    } catch (error) {
      console.error('删除任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const setCurrentTask = async (taskId: string) => {
    try {
      const response = await TaskApi.getTaskDetail(taskId)
      if (response.data) {
        currentTask.value = response.data.task
      }
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    }
  }

  const updateTaskStatus = async (taskId: string, status: string) => {
    try {
      const response = await TaskApi.updateTaskStatus(taskId, status)
      if (response.data) {
        const index = tasks.value.findIndex(task => task.taskId === taskId)
        if (index !== -1) {
          tasks.value[index].status = status
        }
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    loading.value = false
    total.value = 0
    currentPage.value = 1
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    total,
    currentPage,
    pageSize,

    // 计算属性
    tasksBySubject,
    pendingTasks,
    completedTasks,

    // 方法
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    setCurrentTask,
    updateTaskStatus,
    resetState
  }
})
```

### [DEV-STORE-002] 思维导图状态管理
**状态ID**：DEV-STORE-002
**修复目标**：实现MindMapCanvas.vue中使用的useMindMapStore

#### mindmap.ts 状态管理实现
```typescript
// src/stores/mindmap.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { MindMapApi } from '@/services/api/mindmap'
import type {
  MindMap,
  MindMapCreateRequest,
  MindMapUpdateRequest,
  MindMapListParams,
  MindMapData
} from '@/services/types'

export const useMindMapStore = defineStore('mindmap', () => {
  // 状态
  const mindMaps = ref<MindMap[]>([])
  const currentMindMap = ref<MindMap | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const publicMindMaps = computed(() =>
    mindMaps.value.filter(mindMap => mindMap.isPublic)
  )

  const myMindMaps = computed(() =>
    mindMaps.value.filter(mindMap => !mindMap.isPublic)
  )

  // 操作方法
  const fetchMindMaps = async (params: MindMapListParams = {}) => {
    try {
      loading.value = true
      const response = await MindMapApi.getMindMapList({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      })

      mindMaps.value = response.data?.mindMaps || []
      total.value = response.pagination?.total || 0
    } catch (error) {
      console.error('获取思维导图列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createMindMap = async (mindMapData: MindMapCreateRequest) => {
    try {
      loading.value = true
      const response = await MindMapApi.createMindMap(mindMapData)

      if (response.data) {
        mindMaps.value.unshift(response.data)
        total.value += 1
        return response.data
      }
    } catch (error) {
      console.error('创建思维导图失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateMindMap = async (mindMapId: string, mindMapData: MindMapUpdateRequest) => {
    try {
      loading.value = true
      const response = await MindMapApi.updateMindMap(mindMapId, mindMapData)

      if (response.data) {
        const index = mindMaps.value.findIndex(mindMap => mindMap.id === mindMapId)
        if (index !== -1) {
          mindMaps.value[index] = response.data
        }

        if (currentMindMap.value?.id === mindMapId) {
          currentMindMap.value = response.data
        }

        return response.data
      }
    } catch (error) {
      console.error('更新思维导图失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteMindMap = async (mindMapId: string) => {
    try {
      loading.value = true
      await MindMapApi.deleteMindMap(mindMapId)

      mindMaps.value = mindMaps.value.filter(mindMap => mindMap.id !== mindMapId)
      total.value -= 1

      if (currentMindMap.value?.id === mindMapId) {
        currentMindMap.value = null
      }
    } catch (error) {
      console.error('删除思维导图失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getMindMap = async (mindMapId: string) => {
    try {
      const response = await MindMapApi.getMindMapDetail(mindMapId)
      if (response.data) {
        currentMindMap.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取思维导图详情失败:', error)
      throw error
    }
  }

  const linkTaskToNode = async (mindMapId: string, nodeId: string, taskId: string) => {
    try {
      const response = await MindMapApi.linkTaskToNode(mindMapId, nodeId, taskId)
      if (response.data && currentMindMap.value?.id === mindMapId) {
        currentMindMap.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('关联任务失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    mindMaps.value = []
    currentMindMap.value = null
    loading.value = false
    total.value = 0
    currentPage.value = 1
  }

  return {
    // 状态
    mindMaps,
    currentMindMap,
    loading,
    total,
    currentPage,
    pageSize,

    // 计算属性
    publicMindMaps,
    myMindMaps,

    // 方法
    fetchMindMaps,
    createMindMap,
    updateMindMap,
    deleteMindMap,
    getMindMap,
    linkTaskToNode,
    resetState
  }
})
```

### [DEV-STORE-003] 通知状态管理
**状态ID**：DEV-STORE-003
**修复目标**：实现NotificationCenter.vue中使用的useNotificationStore

#### notification.ts 状态管理实现
```typescript
// src/stores/notification.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { NotificationApi } from '@/services/api/notification'
import type {
  Notification as AppNotification,
  NotificationSettings
} from '@/services/types'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<AppNotification[]>([])
  const settings = ref<NotificationSettings>({
    reviewReminders: true,
    taskDeadlines: true,
    loadWarnings: true,
    achievements: true,
    systemNotifications: true,
    reminderTimes: [5, 15, 30],
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00'
    },
    browserNotifications: false,
    emailNotifications: false,
    inAppNotifications: true
  })
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(1)

  // 计算属性
  const unreadCount = computed(() =>
    notifications.value.filter(n => !n.isRead).length
  )

  const unreadNotifications = computed(() =>
    notifications.value.filter(n => !n.isRead)
  )

  // 新通知监听器
  const newNotificationListeners = ref<((notification: AppNotification) => void)[]>([])

  // 操作方法
  const fetchNotifications = async (reset = true) => {
    try {
      loading.value = true
      if (reset) {
        currentPage.value = 1
        notifications.value = []
      }

      const response = await NotificationApi.getNotifications({
        page: currentPage.value,
        pageSize: 20
      })

      if (response.data) {
        if (reset) {
          notifications.value = response.data.notifications
        } else {
          notifications.value.push(...response.data.notifications)
        }

        hasMore.value = response.data.notifications.length === 20
        currentPage.value += 1
      }
    } catch (error) {
      console.error('获取通知列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const loadMoreNotifications = async () => {
    if (!hasMore.value || loading.value) return
    await fetchNotifications(false)
  }

  const markAsRead = async (notificationId: string) => {
    try {
      await NotificationApi.markAsRead(notificationId)

      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
      }
    } catch (error) {
      console.error('标记已读失败:', error)
      throw error
    }
  }

  const markAllAsRead = async () => {
    try {
      await NotificationApi.markAllAsRead()

      notifications.value.forEach(notification => {
        notification.isRead = true
      })
    } catch (error) {
      console.error('标记全部已读失败:', error)
      throw error
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      await NotificationApi.deleteNotification(notificationId)

      notifications.value = notifications.value.filter(n => n.id !== notificationId)
    } catch (error) {
      console.error('删除通知失败:', error)
      throw error
    }
  }

  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    try {
      const response = await NotificationApi.updateNotificationSettings(newSettings)
      if (response.data) {
        settings.value = response.data
      }
    } catch (error) {
      console.error('更新通知设置失败:', error)
      throw error
    }
  }

  const addNotification = (notification: AppNotification) => {
    notifications.value.unshift(notification)

    // 触发新通知监听器
    newNotificationListeners.value.forEach(listener => {
      listener(notification)
    })
  }

  // 新通知监听器管理
  const onNewNotification = (listener: (notification: AppNotification) => void) => {
    newNotificationListeners.value.push(listener)
  }

  const offNewNotification = (listener: (notification: AppNotification) => void) => {
    const index = newNotificationListeners.value.indexOf(listener)
    if (index > -1) {
      newNotificationListeners.value.splice(index, 1)
    }
  }

  // 重置状态
  const resetState = () => {
    notifications.value = []
    loading.value = false
    hasMore.value = true
    currentPage.value = 1
    newNotificationListeners.value = []
  }

  return {
    // 状态
    notifications,
    settings,
    loading,
    hasMore,

    // 计算属性
    unreadCount,
    unreadNotifications,

    // 方法
    fetchNotifications,
    loadMoreNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updateSettings,
    addNotification,
    onNewNotification,
    offNewNotification,
    resetState
  }
})
```

### [DEV-STORE-004] 用户状态管理
**状态ID**：DEV-STORE-004
**修复目标**：实现HTTP拦截器中使用的useUserStore

#### user.ts 状态管理实现
```typescript
// src/stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserApi } from '@/services/api/user'
import type {
  User,
  UserLoginRequest,
  UserRegisterRequest,
  UserUpdateRequest
} from '@/services/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const loading = ref(false)
  const isLoggedIn = computed(() => !!token.value && !!user.value)

  // 从localStorage恢复状态
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('user_info')

    if (savedToken) {
      token.value = savedToken
    }

    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  // 登录
  const login = async (credentials: UserLoginRequest) => {
    try {
      loading.value = true
      const response = await UserApi.login(credentials)

      if (response.data) {
        user.value = response.data.user
        token.value = response.data.token
        isLoggedIn.value = true

        // 保存到localStorage
        localStorage.setItem('auth_token', token.value)
        localStorage.setItem('user_info', JSON.stringify(user.value))

        return response.data
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: UserRegisterRequest) => {
    try {
      loading.value = true
      const response = await UserApi.register(userData)

      if (response.data) {
        user.value = response.data.user
        token.value = response.data.token
        isLoggedIn.value = true

        // 保存到localStorage
        localStorage.setItem('auth_token', token.value)
        localStorage.setItem('user_info', JSON.stringify(user.value))

        return response.data
      }
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = ''

    // 清除localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }

  // 更新用户信息
  const updateUser = async (userData: UserUpdateRequest) => {
    try {
      loading.value = true
      const response = await UserApi.updateUser(userData)

      if (response.data) {
        user.value = response.data

        // 更新localStorage
        localStorage.setItem('user_info', JSON.stringify(user.value))

        return response.data
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 刷新用户信息
  const refreshUser = async () => {
    if (!token.value) return

    try {
      const response = await UserApi.getCurrentUser()
      if (response.data) {
        user.value = response.data
        localStorage.setItem('user_info', JSON.stringify(user.value))
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      // 如果token无效，清除登录状态
      logout()
      throw error
    }
  }

  // 检查token是否有效
  const validateToken = async () => {
    if (!token.value) return false

    try {
      const response = await UserApi.validateToken()
      return response.data?.valid || false
    } catch (error) {
      console.error('Token验证失败:', error)
      logout()
      return false
    }
  }

  // 重置状态
  const resetState = () => {
    user.value = null
    token.value = ''
    loading.value = false
  }

  return {
    // 状态
    user,
    token,
    loading,

    // 计算属性
    isLoggedIn,

    // 方法
    initializeAuth,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
    validateToken,
    resetState
  }
})
```

### [DEV-COMP-003] 负载均衡预警组件
**组件ID**：DEV-COMP-003
**实现需求**：[REQ-FUNC-003] 负载均衡检查功能
**对应设计**：[DES-ALGO-002] 负载均衡计算算法

#### LoadBalanceWarning.vue 组件实现
```vue
<template>
  <div class="load-balance-warning" :class="warningClass">
    <div class="warning-header">
      <el-icon class="warning-icon">
        <Warning />
      </el-icon>
      <span class="warning-title">学习负载预警</span>
      <el-tag :type="levelTagType" size="small">
        {{ levelText }}
      </el-tag>
    </div>

    <div class="warning-content">
      <p class="warning-message">{{ warning.message }}</p>

      <div class="load-details">
        <div class="load-bar">
          <div class="load-progress" :style="{ width: `${warning.currentLoad}%` }"></div>
        </div>
        <span class="load-text">当前负载：{{ warning.currentLoad }}%</span>
      </div>

      <div class="suggestions" v-if="warning.suggestions?.length">
        <h4 class="suggestions-title">调整建议：</h4>
        <ul class="suggestions-list">
          <li v-for="(suggestion, index) in warning.suggestions" :key="index">
            {{ suggestion }}
          </li>
        </ul>
      </div>

      <div class="alternative-dates" v-if="warning.alternativeDates?.length">
        <h4 class="alternatives-title">建议的替代日期：</h4>
        <div class="date-options">
          <el-button
            v-for="date in warning.alternativeDates"
            :key="date"
            size="small"
            @click="selectAlternativeDate(date)"
          >
            {{ formatDate(date) }}
          </el-button>
        </div>
      </div>
    </div>

    <div class="warning-actions">
      <el-button type="primary" @click="handleAdjust">
        接受建议
      </el-button>
      <el-button @click="handleIgnore">
        忽略预警
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import type { LoadWarning } from '@/services/types'

interface Props {
  warning: LoadWarning
}

interface Emits {
  (e: 'adjust', adjustment: { type: string; data: any }): void
  (e: 'ignore'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const warningClass = computed(() => {
  return `warning-${props.warning.level}`
})

const levelTagType = computed(() => {
  switch (props.warning.level) {
    case 'heavy':
      return 'danger'
    case 'medium':
      return 'warning'
    default:
      return 'info'
  }
})

const levelText = computed(() => {
  switch (props.warning.level) {
    case 'heavy':
      return '重度负载'
    case 'medium':
      return '中度负载'
    case 'light':
      return '轻度负载'
    default:
      return '正常负载'
  }
})

// 方法
const selectAlternativeDate = (date: string) => {
  emit('adjust', {
    type: 'reschedule',
    data: { newDate: date }
  })
}

const handleAdjust = () => {
  emit('adjust', {
    type: 'auto_adjust',
    data: {
      suggestions: props.warning.suggestions,
      alternativeDates: props.warning.alternativeDates
    }
  })
}

const handleIgnore = () => {
  emit('ignore')
}
</script>

<style scoped lang="scss">
.load-balance-warning {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  background: #fefefe;

  &.warning-light {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  &.warning-medium {
    border-color: #f59e0b;
    background: #fffbeb;
  }

  &.warning-heavy {
    border-color: #ef4444;
    background: #fef2f2;
  }

  .warning-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .warning-icon {
      font-size: 18px;
      color: #f59e0b;
    }

    .warning-title {
      font-weight: 600;
      color: #374151;
    }
  }

  .warning-content {
    margin-bottom: 16px;

    .warning-message {
      color: #6b7280;
      margin-bottom: 12px;
    }

    .load-details {
      margin-bottom: 16px;

      .load-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 4px;

        .load-progress {
          height: 100%;
          background: linear-gradient(90deg, #10b981, #f59e0b, #ef4444);
          transition: width 0.3s ease;
        }
      }

      .load-text {
        font-size: 12px;
        color: #6b7280;
      }
    }

    .suggestions {
      margin-bottom: 16px;

      .suggestions-title {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
      }

      .suggestions-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 4px 0;
          color: #6b7280;
          font-size: 14px;

          &::before {
            content: '•';
            color: #f59e0b;
            margin-right: 8px;
          }
        }
      }
    }

    .alternative-dates {
      .alternatives-title {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
      }

      .date-options {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
  }

  .warning-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}
</style>
```

### [DEV-COMP-006] 任务列表管理组件
**组件ID**：DEV-COMP-006
**实现需求**：[REQ-FUNC-005] 任务列表管理功能
**对应设计**：[DES-API-001] 创建学习任务

#### TaskList.vue 组件实现
```vue
<template>
  <div class="task-list">
    <!-- 搜索和筛选栏 -->
    <div class="list-header">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索任务标题或内容"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="filter-section">
        <el-select v-model="filters.status" placeholder="状态" clearable @change="handleFilter">
          <el-option label="待开始" value="pending" />
          <el-option label="进行中" value="in_progress" />
          <el-option label="已完成" value="completed" />
          <el-option label="已归档" value="archived" />
        </el-select>

        <el-select v-model="filters.subject" placeholder="学科" clearable @change="handleFilter">
          <el-option v-for="subject in subjectOptions" :key="subject.value"
                     :label="subject.label" :value="subject.value" />
        </el-select>

        <el-select v-model="filters.priority" placeholder="优先级" clearable @change="handleFilter">
          <el-option label="低优先级" :value="1" />
          <el-option label="中优先级" :value="3" />
          <el-option label="高优先级" :value="5" />
        </el-select>
      </div>

      <div class="action-section">
        <el-button type="primary" @click="createTask">
          <el-icon><Plus /></el-icon>
          新建任务
        </el-button>

        <el-dropdown @command="handleBatchAction" :disabled="selectedTasks.length === 0">
          <el-button>
            批量操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="complete">标记完成</el-dropdown-item>
              <el-dropdown-item command="archive">归档</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 排序和视图切换 -->
    <div class="list-controls">
      <div class="sort-section">
        <el-select v-model="sortBy" @change="handleSort">
          <el-option label="创建时间" value="createdAt" />
          <el-option label="更新时间" value="updatedAt" />
          <el-option label="优先级" value="priority" />
          <el-option label="难度" value="difficulty" />
          <el-option label="预估时间" value="estimatedTime" />
        </el-select>

        <el-button @click="toggleSortOrder">
          <el-icon>
            <component :is="sortOrder === 'asc' ? 'SortUp' : 'SortDown'" />
          </el-icon>
        </el-button>
      </div>

      <div class="view-section">
        <el-radio-group v-model="viewMode" @change="handleViewChange">
          <el-radio-button label="list">列表</el-radio-button>
          <el-radio-button label="card">卡片</el-radio-button>
          <el-radio-button label="kanban">看板</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 任务列表内容 -->
    <div class="list-content" v-loading="loading">
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view">
        <el-table
          :data="tasks"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          stripe
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="title" label="任务标题" min-width="200">
            <template #default="{ row }">
              <div class="task-title">
                <span>{{ row.title }}</span>
                <el-tag v-if="row.mindMapId" size="small" type="info">思维导图</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="subject" label="学科" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getSubjectLabel(row.subject) }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-rate v-model="row.priority" disabled size="small" />
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="estimatedTime" label="预估时间" width="100">
            <template #default="{ row }">
              {{ row.estimatedTime }}分钟
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button text size="small" @click.stop="editTask(row)">编辑</el-button>
              <el-button text size="small" @click.stop="viewTask(row)">查看</el-button>
              <el-dropdown @command="(command) => handleTaskAction(command, row)">
                <el-button text size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                    <el-dropdown-item command="archive">归档</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else-if="viewMode === 'card'" class="card-view">
        <div class="card-grid">
          <TaskCard
            v-for="task in tasks"
            :key="task.taskId"
            :task="task"
            :selected="selectedTaskIds.includes(task.taskId)"
            @select="handleCardSelect"
            @click="viewTask"
            @edit="editTask"
            @delete="deleteTask"
          />
        </div>
      </div>

      <!-- 看板视图 -->
      <div v-else-if="viewMode === 'kanban'" class="kanban-view">
        <div class="kanban-columns">
          <div v-for="status in kanbanStatuses" :key="status.value" class="kanban-column">
            <div class="column-header">
              <h3>{{ status.label }}</h3>
              <el-badge :value="getTaskCountByStatus(status.value)" />
            </div>
            <div class="column-content">
              <draggable
                v-model="getTasksByStatus(status.value)"
                group="tasks"
                @change="handleKanbanChange"
                item-key="id"
              >
                <template #item="{ element }">
                  <TaskCard
                    :task="element"
                    compact
                    @click="viewTask"
                    @edit="editTask"
                  />
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="list-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, ArrowDown, SortUp, SortDown } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { useTaskStore } from '@/stores/task'
import TaskCard from '@/components/business/TaskCard.vue'
import { formatDate } from '@/utils/date'
import type { Task } from '@/services/types'

// 路由
const router = useRouter()

// 状态管理
const taskStore = useTaskStore()

// 组件状态
const loading = ref(false)
const searchQuery = ref('')
const viewMode = ref<'list' | 'card' | 'kanban'>('list')
const sortBy = ref('createdAt')
const sortOrder = ref<'asc' | 'desc'>('desc')
const selectedTasks = ref<Task[]>([])
const currentPage = ref(1)
const pageSize = ref(20)

// 筛选条件
const filters = ref({
  status: '',
  subject: '',
  priority: null as number | null
})

// 计算属性
const tasks = computed(() => taskStore.tasks)
const total = computed(() => taskStore.total)
const selectedTaskIds = computed(() => selectedTasks.value.map(task => task.taskId))

// 配置选项
const subjectOptions = [
  { label: '数学', value: 'math' },
  { label: '语文', value: 'chinese' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' }
]

const kanbanStatuses = [
  { label: '待开始', value: 'pending' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已归档', value: 'archived' }
]

// 方法
const loadTasks = async () => {
  try {
    loading.value = true
    await taskStore.fetchTasks({
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      search: searchQuery.value,
      ...filters.value
    })
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = debounce(() => {
  currentPage.value = 1
  loadTasks()
}, 300)

const handleFilter = () => {
  currentPage.value = 1
  loadTasks()
}

const handleSort = () => {
  loadTasks()
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  loadTasks()
}

const handleViewChange = () => {
  // 视图切换逻辑
}

const handleSelectionChange = (selection: Task[]) => {
  selectedTasks.value = selection
}

const handleRowClick = (row: Task) => {
  viewTask(row)
}

const handleCardSelect = (task: Task, selected: boolean) => {
  if (selected) {
    selectedTasks.value.push(task)
  } else {
    const index = selectedTasks.value.findIndex(t => t.taskId === task.taskId)
    if (index > -1) {
      selectedTasks.value.splice(index, 1)
    }
  }
}

const handleBatchAction = async (command: string) => {
  const taskIds = selectedTaskIds.value

  try {
    switch (command) {
      case 'complete':
        await Promise.all(taskIds.map(id => taskStore.updateTaskStatus(id, 'completed')))
        ElMessage.success('批量标记完成成功')
        break
      case 'archive':
        await Promise.all(taskIds.map(id => taskStore.updateTaskStatus(id, 'cancelled')))
        ElMessage.success('批量取消成功')
        break
      case 'delete':
        await ElMessageBox.confirm('确定要删除选中的任务吗？', '确认删除')
        await Promise.all(taskIds.map(id => taskStore.deleteTask(id)))
        ElMessage.success('批量删除成功')
        break
    }

    selectedTasks.value = []
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const handleTaskAction = async (command: string, task: Task) => {
  try {
    switch (command) {
      case 'duplicate':
        // 复制任务逻辑
        break
      case 'archive':
        await taskStore.updateTaskStatus(task.taskId, 'cancelled')
        ElMessage.success('取消成功')
        break
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除')
        await taskStore.deleteTask(task.taskId)
        ElMessage.success('删除成功')
        break
    }
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const createTask = () => {
  router.push('/tasks/create')
}

const editTask = (task: Task) => {
  router.push(`/tasks/${task.taskId}/edit`)
}

const viewTask = (task: Task) => {
  router.push(`/tasks/${task.taskId}`)
}

const deleteTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除')
    await taskStore.deleteTask(task.taskId)
    ElMessage.success('删除成功')
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTasks()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTasks()
}

// 看板相关方法
const getTasksByStatus = (status: string) => {
  return tasks.value.filter(task => task.status === status)
}

const getTaskCountByStatus = (status: string) => {
  return getTasksByStatus(status).length
}

const handleKanbanChange = (event: any) => {
  // 处理看板拖拽变更
  if (event.added) {
    const task = event.added.element
    const newStatus = event.added.newIndex
    // 更新任务状态
  }
}

// 辅助方法
const getSubjectLabel = (subject: string) => {
  const option = subjectOptions.find(opt => opt.value === subject)
  return option?.label || subject
}

const getStatusLabel = (status: string) => {
  const statusMap = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    archived: '已归档'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap = {
    pending: 'info',
    in_progress: 'warning',
    completed: 'success',
    archived: 'info'
  }
  return typeMap[status] || 'info'
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadTasks()
})

// 监听筛选条件变化
watch([searchQuery, filters], () => {
  handleFilter()
}, { deep: true })
</script>

<style scoped lang="scss">
.task-list {
  .list-header {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: center;

    .search-section {
      flex: 1;
      max-width: 300px;
    }

    .filter-section {
      display: flex;
      gap: 8px;
    }

    .action-section {
      display: flex;
      gap: 8px;
    }
  }

  .list-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .sort-section {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  .list-content {
    margin-bottom: 16px;

    .card-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .kanban-view {
      .kanban-columns {
        display: flex;
        gap: 16px;
        overflow-x: auto;

        .kanban-column {
          min-width: 280px;
          background: #f9fafb;
          border-radius: 8px;
          padding: 16px;

          .column-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
            }
          }

          .column-content {
            min-height: 400px;
          }
        }
      }
    }
  }

  .list-pagination {
    display: flex;
    justify-content: center;
  }

  .task-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>
```

### [DEV-COMP-007] 缺失组件补充实现
**组件ID**：DEV-COMP-007
**修复目标**：补充MindMapCanvas.vue等组件中引用的缺失子组件

#### MindMapToolbar.vue 组件实现
```vue
<template>
  <div class="mindmap-toolbar">
    <div class="toolbar-section">
      <el-button-group>
        <el-button size="small" @click="$emit('add-node')">
          <el-icon><Plus /></el-icon>
          添加节点
        </el-button>
        <el-button size="small" @click="$emit('delete-node')">
          <el-icon><Delete /></el-icon>
          删除节点
        </el-button>
      </el-button-group>
    </div>

    <div class="toolbar-section">
      <el-button-group>
        <el-button size="small" @click="$emit('save')">
          <el-icon><DocumentChecked /></el-icon>
          保存
        </el-button>
        <el-button size="small" @click="$emit('export')">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </el-button-group>
    </div>

    <div class="toolbar-section">
      <el-button size="small" @click="$emit('link-task')">
        <el-icon><Link /></el-icon>
        关联任务
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Delete, DocumentChecked, Download, Link } from '@element-plus/icons-vue'

interface Emits {
  (e: 'add-node'): void
  (e: 'delete-node'): void
  (e: 'save'): void
  (e: 'export'): void
  (e: 'link-task'): void
}

defineEmits<Emits>()
</script>

<style scoped lang="scss">
.mindmap-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e5e7eb;

  .toolbar-section {
    display: flex;
    align-items: center;
  }
}
</style>
```

#### NodeEditor.vue 组件实现
```vue
<template>
  <el-dialog
    v-model="visible"
    title="编辑节点"
    width="500px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="节点标题" prop="label">
        <el-input v-model="form.label" placeholder="请输入节点标题" />
      </el-form-item>

      <el-form-item label="节点内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入节点详细内容"
        />
      </el-form-item>

      <el-form-item label="节点类型" prop="type">
        <el-select v-model="form.type" placeholder="选择节点类型">
          <el-option label="根节点" value="root" />
          <el-option label="子节点" value="child" />
          <el-option label="叶子节点" value="leaf" />
        </el-select>
      </el-form-item>

      <el-form-item label="背景颜色">
        <el-color-picker v-model="form.style.backgroundColor" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { MindMapNode } from '@/services/types'

interface Props {
  visible: boolean
  node: MindMapNode | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save', node: MindMapNode): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const form = ref({
  id: '',
  label: '',
  content: '',
  type: 'child' as const,
  style: {
    backgroundColor: '#3b82f6'
  }
})

const rules: FormRules = {
  label: [
    { required: true, message: '请输入节点标题', trigger: 'blur' }
  ]
}

watch(() => props.node, (node) => {
  if (node) {
    form.value = {
      id: node.id,
      label: node.label,
      content: node.content || '',
      type: node.type,
      style: {
        backgroundColor: node.style?.backgroundColor || '#3b82f6'
      }
    }
  }
}, { immediate: true })

const handleClose = () => {
  emit('update:visible', false)
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const nodeData: MindMapNode = {
      id: form.value.id,
      label: form.value.label,
      content: form.value.content,
      type: form.value.type,
      style: form.value.style,
      position: props.node?.position || { x: 0, y: 0 }
    }

    emit('save', nodeData)
  } catch (error) {
    ElMessage.error('请检查输入内容')
  }
}
</script>
```

#### TaskLinkDialog.vue 组件实现
```vue
<template>
  <el-dialog
    v-model="visible"
    title="关联任务"
    width="600px"
    @close="handleClose"
  >
    <div class="task-link-content">
      <div class="current-node">
        <h4>当前节点：{{ node?.label }}</h4>
        <p v-if="node?.taskId" class="linked-task">
          已关联任务：{{ linkedTaskTitle }}
          <el-button text type="danger" @click="unlinkTask">取消关联</el-button>
        </p>
      </div>

      <el-divider />

      <div class="task-search">
        <el-input
          v-model="searchQuery"
          placeholder="搜索任务"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="task-list" v-loading="loading">
        <div
          v-for="task in tasks"
          :key="task.taskId"
          class="task-item"
          :class="{ 'selected': selectedTaskId === task.taskId }"
          @click="selectTask(task.taskId)"
        >
          <div class="task-info">
            <div class="task-title">{{ task.title }}</div>
            <div class="task-meta">
              <el-tag size="small">{{ task.metadata.subject }}</el-tag>
              <span class="task-time">{{ task.metadata.estimatedTime }}分钟</span>
            </div>
          </div>
          <el-icon v-if="selectedTaskId === task.taskId" class="selected-icon">
            <Check />
          </el-icon>
        </div>

        <div v-if="tasks.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无任务" />
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="handleLink"
        :disabled="!selectedTaskId"
      >
        关联任务
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Check } from '@element-plus/icons-vue'
import { useTaskStore } from '@/stores/task'
import type { MindMapNode, Task } from '@/services/types'

interface Props {
  visible: boolean
  node: MindMapNode | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'link', taskId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const taskStore = useTaskStore()

const loading = ref(false)
const searchQuery = ref('')
const selectedTaskId = ref('')

const tasks = computed(() => taskStore.tasks)
const linkedTaskTitle = computed(() => {
  if (!props.node?.taskId) return ''
  const task = tasks.value.find(t => t.id === props.node?.taskId)
  return task?.title || '未知任务'
})

watch(() => props.visible, (visible) => {
  if (visible) {
    loadTasks()
    selectedTaskId.value = props.node?.taskId || ''
  }
})

const loadTasks = async () => {
  try {
    loading.value = true
    await taskStore.fetchTasks({
      page: 1,
      pageSize: 50,
      search: searchQuery.value
    })
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadTasks()
}

const selectTask = (taskId: string) => {
  selectedTaskId.value = taskId
}

const unlinkTask = () => {
  selectedTaskId.value = ''
  emit('link', '')
}

const handleLink = () => {
  if (selectedTaskId.value) {
    emit('link', selectedTaskId.value)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.task-link-content {
  .current-node {
    h4 {
      margin: 0 0 8px 0;
      color: #374151;
    }

    .linked-task {
      color: #6b7280;
      font-size: 14px;
      margin: 0;
    }
  }

  .task-search {
    margin-bottom: 16px;
  }

  .task-list {
    max-height: 300px;
    overflow-y: auto;

    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: #f9fafb;
      }

      &.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
      }

      .task-info {
        flex: 1;

        .task-title {
          font-weight: 500;
          color: #374151;
          margin-bottom: 4px;
        }

        .task-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .task-time {
            color: #6b7280;
            font-size: 12px;
          }
        }
      }

      .selected-icon {
        color: #3b82f6;
      }
    }

    .empty-state {
      text-align: center;
      padding: 20px;
    }
  }
}
</style>
```

#### TaskCard.vue 组件实现
```vue
<template>
  <div
    class="task-card"
    :class="{ 'selected': selected, 'compact': compact }"
    @click="$emit('click', task)"
  >
    <div class="card-header">
      <div class="task-title">{{ task.title }}</div>
      <div class="task-actions">
        <el-dropdown @command="handleAction" trigger="click">
          <el-button text size="small">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">编辑</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="card-content">
      <div class="task-meta">
        <el-tag size="small" :type="getSubjectTagType(task.metadata.subject)">
          {{ getSubjectLabel(task.metadata.subject) }}
        </el-tag>
        <el-tag size="small" :type="getStatusTagType(task.status)">
          {{ getStatusLabel(task.status) }}
        </el-tag>
      </div>

      <div class="task-info">
        <div class="info-item">
          <el-icon><Clock /></el-icon>
          <span>{{ task.metadata.estimatedTime }}分钟</span>
        </div>
        <div class="info-item">
          <el-rate v-model="task.metadata.priority" disabled size="small" />
        </div>
      </div>

      <div v-if="task.content.text && !compact" class="task-description">
        {{ truncateText(task.content.text, 100) }}
      </div>
    </div>

    <div v-if="!compact" class="card-footer">
      <div class="task-date">
        {{ formatDate(task.createdAt) }}
      </div>
      <div class="task-progress" v-if="task.completionRate">
        <el-progress
          :percentage="task.completionRate"
          :stroke-width="4"
          :show-text="false"
        />
      </div>
    </div>

    <!-- 选择框 -->
    <div v-if="selectable" class="selection-checkbox">
      <el-checkbox
        :model-value="selected"
        @change="$emit('select', task, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Clock, MoreFilled } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import type { Task } from '@/services/types'

interface Props {
  task: Task
  selected?: boolean
  compact?: boolean
  selectable?: boolean
}

interface Emits {
  (e: 'click', task: Task): void
  (e: 'edit', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'select', task: Task, selected: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleAction = (command: string) => {
  switch (command) {
    case 'edit':
      emit('edit', props.task)
      break
    case 'delete':
      emit('delete', props.task)
      break
  }
}

const getSubjectLabel = (subject: string) => {
  const subjectMap: Record<string, string> = {
    math: '数学',
    chinese: '语文',
    english: '英语',
    physics: '物理',
    chemistry: '化学',
    biology: '生物',
    history: '历史',
    geography: '地理',
    politics: '政治'
  }
  return subjectMap[subject] || subject
}

const getSubjectTagType = (subject: string) => {
  const typeMap: Record<string, string> = {
    math: 'danger',
    chinese: 'success',
    english: 'warning',
    physics: 'info',
    chemistry: 'primary'
  }
  return typeMap[subject] || 'info'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    archived: '已归档'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    in_progress: 'warning',
    completed: 'success',
    archived: 'info'
  }
  return typeMap[status] || 'info'
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
</script>

<style scoped lang="scss">
.task-card {
  position: relative;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  &.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
  }

  &.compact {
    padding: 12px;

    .card-header {
      margin-bottom: 8px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .task-title {
      font-weight: 600;
      color: #374151;
      line-height: 1.4;
      flex: 1;
      margin-right: 8px;
    }

    .task-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }
  }

  &:hover .task-actions {
    opacity: 1;
  }

  .card-content {
    .task-meta {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
    }

    .task-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #6b7280;
        font-size: 14px;
      }
    }

    .task-description {
      color: #6b7280;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f3f4f6;

    .task-date {
      color: #9ca3af;
      font-size: 12px;
    }

    .task-progress {
      flex: 1;
      max-width: 100px;
      margin-left: 12px;
    }
  }

  .selection-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
  }
}
</style>
```

#### NotificationSettings.vue 组件实现
```vue
<template>
  <el-dialog
    v-model="visible"
    title="通知设置"
    width="600px"
    @close="handleClose"
  >
    <el-form :model="form" label-width="120px">
      <el-card class="setting-section">
        <template #header>
          <span>通知类型</span>
        </template>

        <el-form-item label="复习提醒">
          <el-switch v-model="form.reviewReminders" />
        </el-form-item>

        <el-form-item label="任务截止提醒">
          <el-switch v-model="form.taskDeadlines" />
        </el-form-item>

        <el-form-item label="负载预警">
          <el-switch v-model="form.loadWarnings" />
        </el-form-item>

        <el-form-item label="成就通知">
          <el-switch v-model="form.achievements" />
        </el-form-item>

        <el-form-item label="系统通知">
          <el-switch v-model="form.systemNotifications" />
        </el-form-item>
      </el-card>

      <el-card class="setting-section">
        <template #header>
          <span>提醒时间</span>
        </template>

        <el-form-item label="提前提醒">
          <el-checkbox-group v-model="form.reminderTimes">
            <el-checkbox :label="5">5分钟前</el-checkbox>
            <el-checkbox :label="15">15分钟前</el-checkbox>
            <el-checkbox :label="30">30分钟前</el-checkbox>
            <el-checkbox :label="60">1小时前</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>

      <el-card class="setting-section">
        <template #header>
          <span>免打扰时间</span>
        </template>

        <el-form-item label="启用免打扰">
          <el-switch v-model="form.quietHours.enabled" />
        </el-form-item>

        <el-form-item
          label="免打扰时段"
          v-if="form.quietHours.enabled"
        >
          <div class="time-range">
            <el-time-picker
              v-model="form.quietHours.startTime"
              format="HH:mm"
              placeholder="开始时间"
            />
            <span class="time-separator">至</span>
            <el-time-picker
              v-model="form.quietHours.endTime"
              format="HH:mm"
              placeholder="结束时间"
            />
          </div>
        </el-form-item>
      </el-card>

      <el-card class="setting-section">
        <template #header>
          <span>通知方式</span>
        </template>

        <el-form-item label="浏览器通知">
          <el-switch
            v-model="form.browserNotifications"
            @change="handleBrowserNotificationChange"
          />
        </el-form-item>

        <el-form-item label="邮件通知">
          <el-switch v-model="form.emailNotifications" />
        </el-form-item>

        <el-form-item label="应用内通知">
          <el-switch v-model="form.inAppNotifications" />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存设置</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { NotificationApi } from '@/services/api/notification'
import type { NotificationSettings } from '@/services/types'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save', settings: NotificationSettings): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const form = ref<NotificationSettings>({
  reviewReminders: true,
  taskDeadlines: true,
  loadWarnings: true,
  achievements: true,
  systemNotifications: true,
  reminderTimes: [5, 15, 30],
  quietHours: {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00'
  },
  browserNotifications: false,
  emailNotifications: false,
  inAppNotifications: true
})

watch(() => props.visible, async (visible) => {
  if (visible) {
    await loadSettings()
  }
})

const loadSettings = async () => {
  try {
    const response = await NotificationApi.getNotificationSettings()
    if (response.data) {
      form.value = { ...response.data }
    }
  } catch (error) {
    ElMessage.error('加载设置失败')
  }
}

const handleBrowserNotificationChange = async (enabled: boolean) => {
  if (enabled) {
    const granted = await NotificationApi.requestBrowserPermission()
    if (!granted) {
      form.value.browserNotifications = false
      ElMessage.warning('浏览器通知权限被拒绝')
    }
  }
}

const handleSave = () => {
  emit('save', form.value)
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.setting-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12px;

  .time-separator {
    color: #6b7280;
  }
}
</style>
```

### [DEV-COMP-004] 思维导图核心组件
**组件ID**：DEV-COMP-004
**实现需求**：[REQ-FUNC-009] 思维导图创建功能
**对应设计**：[DES-UI-005] 思维导图渲染

#### MindMapCanvas.vue 组件实现
```vue
<template>
  <div class="mindmap-canvas">
    <div class="mindmap-toolbar">
      <MindMapToolbar
        @add-node="handleAddNode"
        @delete-node="handleDeleteNode"
        @save="handleSave"
        @export="handleExport"
        @link-task="handleLinkTask"
      />
    </div>

    <div class="mindmap-container">
      <div
        ref="cytoscapeContainer"
        class="cytoscape-container"
        @contextmenu.prevent="handleContextMenu"
      ></div>

      <!-- 右键菜单 -->
      <div
        v-if="contextMenu.visible"
        class="context-menu"
        :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      >
        <ul>
          <li @click="addChildNode">添加子节点</li>
          <li @click="editNode">编辑节点</li>
          <li @click="deleteNode">删除节点</li>
          <li @click="linkToTask">关联任务</li>
        </ul>
      </div>
    </div>

    <!-- 节点编辑对话框 -->
    <NodeEditor
      v-model:visible="nodeEditorVisible"
      :node="currentNode"
      @save="handleNodeSave"
    />

    <!-- 任务关联对话框 -->
    <TaskLinkDialog
      v-model:visible="taskLinkVisible"
      :node="currentNode"
      @link="handleTaskLink"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import cytoscape, { type Core, type NodeSingular, type EdgeSingular } from 'cytoscape'
import dagre from 'cytoscape-dagre'
import coseBilkent from 'cytoscape-cose-bilkent'
import { ElMessage } from 'element-plus'
import MindMapToolbar from './MindMapToolbar.vue'
import NodeEditor from './NodeEditor.vue'
import TaskLinkDialog from './TaskLinkDialog.vue'
import { useMindMapStore } from '@/stores/mindmap'
import type { MindMapNode, MindMapEdge, MindMapData } from '@/services/types'

// 注册Cytoscape扩展
cytoscape.use(dagre)
cytoscape.use(coseBilkent)

interface Props {
  mindMapId?: string
  readonly?: boolean
  initialData?: MindMapData
}

interface Emits {
  (e: 'save', data: MindMapData): void
  (e: 'change', data: MindMapData): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<Emits>()

// 状态管理
const mindMapStore = useMindMapStore()

// 组件引用
const cytoscapeContainer = ref<HTMLElement>()
let cy: Core | null = null

// 组件状态
const nodeEditorVisible = ref(false)
const taskLinkVisible = ref(false)
const currentNode = ref<MindMapNode | null>(null)
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  targetNode: null as NodeSingular | null
})

// Cytoscape样式配置
const cytoscapeStyle = [
  {
    selector: 'node',
    style: {
      'background-color': '#3b82f6',
      'label': 'data(label)',
      'text-valign': 'center',
      'text-halign': 'center',
      'color': '#ffffff',
      'font-size': '12px',
      'font-weight': 'bold',
      'width': 'label',
      'height': 'label',
      'padding': '8px',
      'shape': 'round-rectangle',
      'border-width': 2,
      'border-color': '#1e40af'
    }
  },
  {
    selector: 'node:selected',
    style: {
      'border-color': '#f59e0b',
      'border-width': 3
    }
  },
  {
    selector: 'node[type="root"]',
    style: {
      'background-color': '#10b981',
      'border-color': '#059669',
      'font-size': '14px'
    }
  },
  {
    selector: 'node[hasTask="true"]',
    style: {
      'background-color': '#8b5cf6',
      'border-color': '#7c3aed'
    }
  },
  {
    selector: 'edge',
    style: {
      'width': 2,
      'line-color': '#6b7280',
      'target-arrow-color': '#6b7280',
      'target-arrow-shape': 'triangle',
      'curve-style': 'bezier'
    }
  },
  {
    selector: 'edge:selected',
    style: {
      'line-color': '#f59e0b',
      'target-arrow-color': '#f59e0b',
      'width': 3
    }
  }
]

// 初始化Cytoscape
const initCytoscape = async () => {
  if (!cytoscapeContainer.value) return

  cy = cytoscape({
    container: cytoscapeContainer.value,
    style: cytoscapeStyle,
    layout: {
      name: 'dagre',
      rankDir: 'TB',
      spacingFactor: 1.5
    },
    elements: [],
    minZoom: 0.5,
    maxZoom: 2,
    wheelSensitivity: 0.2
  })

  // 绑定事件
  bindEvents()

  // 加载数据
  if (props.initialData) {
    loadMindMapData(props.initialData)
  } else if (props.mindMapId) {
    await loadMindMap(props.mindMapId)
  } else {
    // 创建默认根节点
    createRootNode()
  }
}

// 绑定事件
const bindEvents = () => {
  if (!cy) return

  // 节点双击编辑
  cy.on('dbltap', 'node', (event) => {
    if (props.readonly) return
    const node = event.target
    editNodeById(node.id())
  })

  // 右键菜单
  cy.on('cxttap', 'node', (event) => {
    if (props.readonly) return
    const node = event.target
    const position = event.renderedPosition
    showContextMenu(position.x, position.y, node)
  })

  // 点击空白处隐藏右键菜单
  cy.on('tap', (event) => {
    if (event.target === cy) {
      hideContextMenu()
    }
  })

  // 数据变化监听
  cy.on('add remove move', () => {
    emitChange()
  })
}

// 加载思维导图数据
const loadMindMap = async (mindMapId: string) => {
  try {
    const mindMapData = await mindMapStore.getMindMap(mindMapId)
    if (mindMapData) {
      loadMindMapData(mindMapData)
    }
  } catch (error) {
    ElMessage.error('加载思维导图失败')
  }
}

// 加载思维导图数据到Cytoscape
const loadMindMapData = (data: MindMapData) => {
  if (!cy) return

  const elements = [
    ...data.nodes.map(node => ({
      data: {
        id: node.id,
        label: node.label,
        type: node.type,
        hasTask: !!node.taskId,
        taskId: node.taskId,
        content: node.content,
        style: node.style
      },
      position: node.position
    })),
    ...data.edges.map(edge => ({
      data: {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        label: edge.label
      }
    }))
  ]

  cy.elements().remove()
  cy.add(elements)
  cy.layout({ name: 'dagre', rankDir: 'TB' }).run()
}

// 创建根节点
const createRootNode = () => {
  if (!cy) return

  const rootNode = {
    data: {
      id: 'root',
      label: '中心主题',
      type: 'root'
    },
    position: { x: 0, y: 0 }
  }

  cy.add(rootNode)
}

// 显示右键菜单
const showContextMenu = (x: number, y: number, node: NodeSingular) => {
  contextMenu.value = {
    visible: true,
    x,
    y,
    targetNode: node
  }
}

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.visible = false
  contextMenu.value.targetNode = null
}

// 添加子节点
const addChildNode = () => {
  if (!cy || !contextMenu.value.targetNode) return

  const parentNode = contextMenu.value.targetNode
  const nodeId = `node_${Date.now()}`

  const newNode = {
    data: {
      id: nodeId,
      label: '新节点',
      type: 'child'
    }
  }

  const newEdge = {
    data: {
      id: `edge_${Date.now()}`,
      source: parentNode.id(),
      target: nodeId
    }
  }

  cy.add([newNode, newEdge])
  cy.layout({ name: 'dagre', rankDir: 'TB' }).run()

  hideContextMenu()
  emitChange()
}

// 编辑节点
const editNode = () => {
  if (!contextMenu.value.targetNode) return
  editNodeById(contextMenu.value.targetNode.id())
  hideContextMenu()
}

// 根据ID编辑节点
const editNodeById = (nodeId: string) => {
  if (!cy) return

  const node = cy.getElementById(nodeId)
  if (node.length === 0) return

  const nodeData = node.data()
  currentNode.value = {
    id: nodeData.id,
    label: nodeData.label,
    type: nodeData.type,
    content: nodeData.content || '',
    taskId: nodeData.taskId,
    style: nodeData.style || {},
    position: node.position()
  }

  nodeEditorVisible.value = true
}

// 删除节点
const deleteNode = () => {
  if (!cy || !contextMenu.value.targetNode) return

  const node = contextMenu.value.targetNode

  // 不允许删除根节点
  if (node.data('type') === 'root') {
    ElMessage.warning('不能删除根节点')
    return
  }

  node.remove()
  hideContextMenu()
  emitChange()
}

// 关联任务
const linkToTask = () => {
  if (!contextMenu.value.targetNode) return

  const node = contextMenu.value.targetNode
  const nodeData = node.data()

  currentNode.value = {
    id: nodeData.id,
    label: nodeData.label,
    type: nodeData.type,
    content: nodeData.content || '',
    taskId: nodeData.taskId,
    style: nodeData.style || {},
    position: node.position()
  }

  taskLinkVisible.value = true
  hideContextMenu()
}

// 处理节点保存
const handleNodeSave = (nodeData: MindMapNode) => {
  if (!cy) return

  const node = cy.getElementById(nodeData.id)
  if (node.length === 0) return

  // 更新节点数据
  node.data({
    ...node.data(),
    label: nodeData.label,
    content: nodeData.content,
    style: nodeData.style
  })

  // 更新位置
  if (nodeData.position) {
    node.position(nodeData.position)
  }

  nodeEditorVisible.value = false
  emitChange()
}

// 处理任务关联
const handleTaskLink = (taskId: string) => {
  if (!cy || !currentNode.value) return

  const node = cy.getElementById(currentNode.value.id)
  if (node.length === 0) return

  // 更新节点数据
  node.data({
    ...node.data(),
    taskId,
    hasTask: !!taskId
  })

  taskLinkVisible.value = false
  emitChange()
}

// 工具栏事件处理
const handleAddNode = () => {
  // 在根节点添加子节点
  const rootNode = cy?.getElementById('root')
  if (rootNode && rootNode.length > 0) {
    contextMenu.value.targetNode = rootNode
    addChildNode()
  }
}

const handleDeleteNode = () => {
  const selectedNodes = cy?.nodes(':selected')
  if (selectedNodes && selectedNodes.length > 0) {
    selectedNodes.forEach(node => {
      if (node.data('type') !== 'root') {
        node.remove()
      }
    })
    emitChange()
  }
}

const handleSave = async () => {
  const data = getMindMapData()
  emit('save', data)

  if (props.mindMapId) {
    try {
      await mindMapStore.updateMindMap(props.mindMapId, data)
      ElMessage.success('保存成功')
    } catch (error) {
      ElMessage.error('保存失败')
    }
  }
}

const handleExport = () => {
  // 导出功能实现
  const data = getMindMapData()
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'mindmap.json'
  a.click()
  URL.revokeObjectURL(url)
}

const handleLinkTask = () => {
  const selectedNodes = cy?.nodes(':selected')
  if (selectedNodes && selectedNodes.length > 0) {
    const node = selectedNodes[0]
    const nodeData = node.data()

    currentNode.value = {
      id: nodeData.id,
      label: nodeData.label,
      type: nodeData.type,
      content: nodeData.content || '',
      taskId: nodeData.taskId,
      style: nodeData.style || {},
      position: node.position()
    }

    taskLinkVisible.value = true
  } else {
    ElMessage.warning('请先选择一个节点')
  }
}

// 获取思维导图数据
const getMindMapData = (): MindMapData => {
  if (!cy) return { nodes: [], edges: [] }

  const nodes: MindMapNode[] = cy.nodes().map(node => ({
    id: node.id(),
    label: node.data('label'),
    type: node.data('type'),
    content: node.data('content') || '',
    taskId: node.data('taskId'),
    style: node.data('style') || {},
    position: node.position()
  }))

  const edges: MindMapEdge[] = cy.edges().map(edge => ({
    id: edge.id(),
    source: edge.source().id(),
    target: edge.target().id(),
    label: edge.data('label') || ''
  }))

  return { nodes, edges }
}

// 发出变化事件
const emitChange = () => {
  const data = getMindMapData()
  emit('change', data)
}

// 处理右键菜单
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCytoscape()
  })
})

onUnmounted(() => {
  if (cy) {
    cy.destroy()
  }
})

// 暴露方法
defineExpose({
  getCytoscape: () => cy,
  getMindMapData,
  loadMindMapData
})
</script>

<style scoped lang="scss">
.mindmap-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f9fafb;

  .mindmap-toolbar {
    flex-shrink: 0;
    border-bottom: 1px solid #e5e7eb;
    background: white;
  }

  .mindmap-container {
    flex: 1;
    position: relative;
    overflow: hidden;

    .cytoscape-container {
      width: 100%;
      height: 100%;
    }

    .context-menu {
      position: absolute;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;

      ul {
        list-style: none;
        margin: 0;
        padding: 4px 0;

        li {
          padding: 8px 16px;
          cursor: pointer;
          font-size: 14px;
          color: #374151;

          &:hover {
            background: #f3f4f6;
          }
        }
      }
    }
  }
}
</style>
```

### [DEV-COMP-005] 复习提醒功能组件
**组件ID**：DEV-COMP-005
**实现需求**：[REQ-FUNC-004] 复习提醒功能
**对应设计**：[DES-API-003] 提醒机制API

#### NotificationCenter.vue 组件实现
```vue
<template>
  <div class="notification-center">
    <!-- 通知图标和徽章 -->
    <div class="notification-trigger" @click="togglePanel">
      <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
        <el-icon class="notification-icon" :class="{ 'has-unread': unreadCount > 0 }">
          <Bell />
        </el-icon>
      </el-badge>
    </div>

    <!-- 通知面板 -->
    <div v-if="panelVisible" class="notification-panel" v-click-outside="closePanel">
      <div class="panel-header">
        <h3>通知中心</h3>
        <div class="header-actions">
          <el-button text size="small" @click="markAllAsRead" :disabled="unreadCount === 0">
            全部已读
          </el-button>
          <el-button text size="small" @click="openSettings">
            设置
          </el-button>
        </div>
      </div>

      <div class="panel-content">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>

        <div v-else-if="notifications.length === 0" class="empty-state">
          <el-empty description="暂无通知" />
        </div>

        <div v-else class="notification-list">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.isRead }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon-wrapper">
              <el-icon class="type-icon" :class="`type-${notification.type}`">
                <component :is="getNotificationIcon(notification.type)" />
              </el-icon>
            </div>

            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
            </div>

            <div class="notification-actions">
              <el-button
                text
                size="small"
                @click.stop="markAsRead(notification.id)"
                v-if="!notification.isRead"
              >
                标记已读
              </el-button>
              <el-button
                text
                size="small"
                @click.stop="deleteNotification(notification.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="panel-footer">
        <el-button text @click="loadMore" :loading="loadingMore" v-if="hasMore">
          加载更多
        </el-button>
      </div>
    </div>

    <!-- 通知设置对话框 -->
    <NotificationSettings
      v-model:visible="settingsVisible"
      @save="handleSettingsSave"
    />

    <!-- 浮动通知 -->
    <div class="floating-notifications">
      <transition-group name="notification-slide" tag="div">
        <div
          v-for="floatingNotification in floatingNotifications"
          :key="floatingNotification.id"
          class="floating-notification"
          :class="`type-${floatingNotification.type}`"
        >
          <div class="floating-content">
            <div class="floating-title">{{ floatingNotification.title }}</div>
            <div class="floating-message">{{ floatingNotification.message }}</div>
          </div>
          <el-button
            text
            size="small"
            @click="dismissFloatingNotification(floatingNotification.id)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell, Clock, Warning, Trophy, InfoFilled, Close } from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { NotificationApi } from '@/services/api/notification'
import NotificationSettings from './NotificationSettings.vue'
import { formatTime } from '@/utils/date'
import type { Notification as AppNotification } from '@/services/types'

// 状态管理
const notificationStore = useNotificationStore()

// 组件状态
const panelVisible = ref(false)
const settingsVisible = ref(false)
const loading = ref(false)
const loadingMore = ref(false)
const floatingNotifications = ref<AppNotification[]>([])

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)
const hasMore = computed(() => notificationStore.hasMore)

// 通知图标映射
const notificationIcons = {
  review_reminder: Clock,
  task_deadline: Warning,
  load_warning: Warning,
  achievement: Trophy,
  system: InfoFilled
}

// 方法
const togglePanel = () => {
  panelVisible.value = !panelVisible.value
  if (panelVisible.value && notifications.value.length === 0) {
    loadNotifications()
  }
}

const closePanel = () => {
  panelVisible.value = false
}

const loadNotifications = async () => {
  try {
    loading.value = true
    await notificationStore.fetchNotifications()
  } catch (error) {
    ElMessage.error('加载通知失败')
  } finally {
    loading.value = false
  }
}

const loadMore = async () => {
  try {
    loadingMore.value = true
    await notificationStore.loadMoreNotifications()
  } catch (error) {
    ElMessage.error('加载更多通知失败')
  } finally {
    loadingMore.value = false
  }
}

const markAsRead = async (notificationId: string) => {
  try {
    await notificationStore.markAsRead(notificationId)
  } catch (error) {
    ElMessage.error('标记已读失败')
  }
}

const markAllAsRead = async () => {
  try {
    await notificationStore.markAllAsRead()
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteNotification = async (notificationId: string) => {
  try {
    await notificationStore.deleteNotification(notificationId)
    ElMessage.success('删除成功')
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleNotificationClick = (notification: AppNotification) => {
  // 根据通知类型执行相应操作
  switch (notification.type) {
    case 'review_reminder':
      // 跳转到复习页面
      if (notification.data?.reviewId) {
        // router.push(`/review/${notification.data.reviewId}`)
      }
      break
    case 'task_deadline':
      // 跳转到任务详情
      if (notification.data?.taskId) {
        // router.push(`/tasks/${notification.data.taskId}`)
      }
      break
    default:
      break
  }

  // 标记为已读
  if (!notification.isRead) {
    markAsRead(notification.id)
  }
}

const openSettings = () => {
  settingsVisible.value = true
}

const handleSettingsSave = async (settings: any) => {
  try {
    await notificationStore.updateSettings(settings)
    ElMessage.success('设置保存成功')
    settingsVisible.value = false
  } catch (error) {
    ElMessage.error('设置保存失败')
  }
}

const getNotificationIcon = (type: AppNotification['type']) => {
  return notificationIcons[type] || InfoFilled
}

const showFloatingNotification = (notification: AppNotification) => {
  floatingNotifications.value.push(notification)

  // 自动消失
  setTimeout(() => {
    dismissFloatingNotification(notification.id)
  }, 5000)
}

const dismissFloatingNotification = (notificationId: string) => {
  const index = floatingNotifications.value.findIndex(n => n.id === notificationId)
  if (index > -1) {
    floatingNotifications.value.splice(index, 1)
  }
}

// WebSocket连接处理新通知
const handleNewNotification = (notification: AppNotification) => {
  notificationStore.addNotification(notification)

  // 显示浮动通知
  if (notificationStore.settings.inAppNotifications) {
    showFloatingNotification(notification)
  }

  // 发送浏览器通知
  if (notificationStore.settings.browserNotifications) {
    NotificationApi.sendBrowserNotification(notification.title, {
      body: notification.message,
      tag: notification.id,
      data: notification.data
    })
  }
}

// 生命周期
onMounted(() => {
  // 请求浏览器通知权限
  NotificationApi.requestBrowserPermission()

  // 监听新通知
  notificationStore.onNewNotification(handleNewNotification)
})

onUnmounted(() => {
  notificationStore.offNewNotification(handleNewNotification)
})
</script>

<style scoped lang="scss">
.notification-center {
  position: relative;

  .notification-trigger {
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f3f4f6;
    }

    .notification-icon {
      font-size: 20px;
      color: #6b7280;
      transition: color 0.2s;

      &.has-unread {
        color: #3b82f6;
      }
    }
  }

  .notification-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 380px;
    max-height: 500px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .panel-content {
      max-height: 350px;
      overflow-y: auto;

      .loading-state,
      .empty-state {
        padding: 20px;
      }

      .notification-list {
        .notification-item {
          display: flex;
          padding: 12px 16px;
          border-bottom: 1px solid #f3f4f6;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background-color: #f9fafb;
          }

          &.unread {
            background-color: #eff6ff;
            border-left: 3px solid #3b82f6;
          }

          .notification-icon-wrapper {
            flex-shrink: 0;
            margin-right: 12px;

            .type-icon {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              &.type-review_reminder {
                background-color: #dbeafe;
                color: #3b82f6;
              }

              &.type-task_deadline {
                background-color: #fef3c7;
                color: #f59e0b;
              }

              &.type-load_warning {
                background-color: #fee2e2;
                color: #ef4444;
              }

              &.type-achievement {
                background-color: #d1fae5;
                color: #10b981;
              }

              &.type-system {
                background-color: #e5e7eb;
                color: #6b7280;
              }
            }
          }

          .notification-content {
            flex: 1;
            min-width: 0;

            .notification-title {
              font-weight: 600;
              color: #374151;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .notification-message {
              color: #6b7280;
              font-size: 14px;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .notification-time {
              color: #9ca3af;
              font-size: 12px;
            }
          }

          .notification-actions {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s;
          }

          &:hover .notification-actions {
            opacity: 1;
          }
        }
      }
    }

    .panel-footer {
      padding: 12px 16px;
      border-top: 1px solid #e5e7eb;
      text-align: center;
    }
  }

  .floating-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;

    .floating-notification {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 16px;
      margin-bottom: 12px;
      max-width: 320px;
      display: flex;
      align-items: flex-start;
      gap: 12px;

      &.type-review_reminder {
        border-left: 4px solid #3b82f6;
      }

      &.type-task_deadline {
        border-left: 4px solid #f59e0b;
      }

      &.type-load_warning {
        border-left: 4px solid #ef4444;
      }

      &.type-achievement {
        border-left: 4px solid #10b981;
      }

      .floating-content {
        flex: 1;

        .floating-title {
          font-weight: 600;
          color: #374151;
          margin-bottom: 4px;
        }

        .floating-message {
          color: #6b7280;
          font-size: 14px;
        }
      }
    }
  }
}

// 动画
.notification-slide-enter-active,
.notification-slide-leave-active {
  transition: all 0.3s ease;
}

.notification-slide-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-slide-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
```

## 📦 项目配置文件

### [DEV-CONFIG-001] Package.json 依赖配置
**配置ID**：DEV-CONFIG-001
**包管理器**：npm

#### package.json 完整配置
```json
{
  "name": "ebbinghaus-frontend",
  "version": "1.0.0",
  "description": "艾宾浩斯记忆曲线学习管理系统前端应用",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "format": "prettier --write src/",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.3.4",
    "vue-router": "^4.2.4",
    "pinia": "^2.1.6",
    "element-plus": "^2.3.9",
    "@element-plus/icons-vue": "^2.1.0",
    "tailwindcss": "^3.3.3",
    "axios": "^1.5.0",
    "dayjs": "^1.11.9",
    "cytoscape": "^3.26.0",
    "cytoscape-dagre": "^2.5.0",
    "cytoscape-cose-bilkent": "^4.1.0",
    "vuedraggable": "^4.1.0",
    "lodash-es": "^4.17.21"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.3.4",
    "vite": "^4.4.9",
    "vue-tsc": "^1.8.8",
    "typescript": "^5.1.6",
    "@vue/test-utils": "^2.4.1",
    "vitest": "^0.34.3",
    "@vitest/ui": "^0.34.3",
    "playwright": "^1.37.1",
    "@playwright/test": "^1.37.1",
    "eslint": "^8.47.0",
    "@typescript-eslint/eslint-plugin": "^6.4.1",
    "@typescript-eslint/parser": "^6.4.1",
    "eslint-plugin-vue": "^9.17.0",
    "prettier": "^3.0.2",
    "@types/lodash-es": "^4.17.8",
    "autoprefixer": "^10.4.15",
    "postcss": "^8.4.28"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

## 🧪 测试验证方法

### [DEV-TEST-001] 单元测试配置
**测试ID**：DEV-TEST-001
**测试框架**：Vitest + Vue Test Utils

#### 组件测试示例
```typescript
// tests/components/TaskCreate.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'
import TaskCreate from '@/views/task/TaskCreate.vue'
import { useTaskStore } from '@/stores/task'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('TaskCreate.vue', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('应该正确渲染任务创建表单', () => {
    const wrapper = mount(TaskCreate)

    expect(wrapper.find('[data-test="task-title"]').exists()).toBe(true)
    expect(wrapper.find('[data-test="task-content"]').exists()).toBe(true)
    expect(wrapper.find('[data-test="task-subject"]').exists()).toBe(true)
  })

  it('应该验证必填字段', async () => {
    const wrapper = mount(TaskCreate)
    const submitButton = wrapper.find('[data-test="submit-button"]')

    await submitButton.trigger('click')

    // 验证错误提示
    expect(wrapper.text()).toContain('请输入任务标题')
    expect(wrapper.text()).toContain('请输入任务内容')
  })

  it('应该成功创建任务', async () => {
    const wrapper = mount(TaskCreate)
    const taskStore = useTaskStore()

    // Mock store方法
    vi.spyOn(taskStore, 'createTask').mockResolvedValue({
      task: { id: '1', title: '测试任务' },
      reviewSchedule: [
        {
          id: '1_review_1',
          taskId: '1',
          reviewIndex: 1,
          intervalDays: 1,
          reviewTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          status: 'scheduled' as const,
          effectiveness: null,
          notes: '',
          confidence: null,
          needsMoreReview: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      loadWarning: null
    })

    // 填写表单
    await wrapper.find('[data-test="task-title"]').setValue('测试任务')
    await wrapper.find('[data-test="task-content"]').setValue('测试内容')
    await wrapper.find('[data-test="task-subject"]').setValue('math')

    // 提交表单
    await wrapper.find('[data-test="submit-button"]').trigger('click')

    expect(taskStore.createTask).toHaveBeenCalledWith({
      title: '测试任务',
      content: '测试内容',
      subject: 'math',
      estimatedTime: 30,
      priority: 3,
      difficulty: 3,
      tags: []
    })
  })
})
```

### [DEV-TEST-002] E2E测试配置
**测试ID**：DEV-TEST-002
**测试框架**：Playwright

#### E2E测试示例
```typescript
// tests/e2e/task-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('任务管理功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    // 登录操作
    await page.fill('[data-test="username"]', 'testuser')
    await page.fill('[data-test="password"]', 'password')
    await page.click('[data-test="login-button"]')
  })

  test('应该能够创建新任务', async ({ page }) => {
    // 导航到任务创建页面
    await page.click('[data-test="create-task-button"]')

    // 填写任务信息
    await page.fill('[data-test="task-title"]', '学习Vue 3组合式API')
    await page.fill('[data-test="task-content"]', '深入学习Vue 3的组合式API特性')
    await page.selectOption('[data-test="task-subject"]', 'programming')

    // 提交表单
    await page.click('[data-test="submit-button"]')

    // 验证成功消息
    await expect(page.locator('.el-message--success')).toContainText('任务创建成功')

    // 验证跳转到任务列表
    await expect(page).toHaveURL('/tasks')

    // 验证任务出现在列表中
    await expect(page.locator('[data-test="task-list"]')).toContainText('学习Vue 3组合式API')
  })

  test('应该显示负载预警', async ({ page }) => {
    // 创建多个任务以触发负载预警
    for (let i = 0; i < 5; i++) {
      await page.click('[data-test="create-task-button"]')
      await page.fill('[data-test="task-title"]', `任务 ${i + 1}`)
      await page.fill('[data-test="task-content"]', `任务内容 ${i + 1}`)
      await page.selectOption('[data-test="task-subject"]', 'math')
      await page.fill('[data-test="estimated-time"]', '60')
      await page.click('[data-test="submit-button"]')
    }

    // 再次创建任务，应该触发负载预警
    await page.click('[data-test="create-task-button"]')
    await page.fill('[data-test="task-title"]', '触发预警的任务')
    await page.fill('[data-test="task-content"]', '这个任务应该触发负载预警')
    await page.selectOption('[data-test="task-subject"]', 'math')
    await page.fill('[data-test="estimated-time"]', '120')

    // 验证负载预警出现
    await expect(page.locator('[data-test="load-warning"]')).toBeVisible()
    await expect(page.locator('[data-test="load-warning"]')).toContainText('学习负载预警')
  })
})
```

## 🚀 部署说明

### [DEV-DEPLOY-001] 开发环境部署
**部署ID**：DEV-DEPLOY-001
**环境要求**：基于 [REQ-NFUNC-006] 浏览器兼容性要求

#### 开发服务器配置
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env.local

# 编辑 .env.local
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=艾宾浩斯学习管理系统
VITE_APP_VERSION=1.0.0

# 3. 启动开发服务器
npm run dev

# 4. 运行测试
npm run test:unit
npm run test:e2e
```

### [DEV-DEPLOY-002] 生产环境构建
**部署ID**：DEV-DEPLOY-002
**性能要求**：满足 [REQ-NFUNC-001] 响应时间要求

#### 构建配置优化
```typescript
// vite.config.ts 生产优化配置
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },

  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,

    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs', 'lodash-es']
        }
      }
    },

    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // 性能优化
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'element-plus']
  }
})
```

#### 构建和部署脚本
```bash
# build.sh
#!/bin/bash

echo "开始构建前端应用..."

# 1. 清理旧的构建文件
rm -rf dist

# 2. 安装依赖
npm ci

# 3. 运行测试
npm run test:unit

# 4. 构建生产版本
npm run build

# 5. 验证构建结果
if [ -d "dist" ]; then
  echo "构建成功！"
  echo "构建文件大小："
  du -sh dist/*
else
  echo "构建失败！"
  exit 1
fi

echo "前端应用构建完成"
```



http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API代理
        location /api/ {
            proxy_pass http://backend:3000/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
```

## 📋 开发检查清单

### [DEV-CHECKLIST-001] 功能开发检查
**检查ID**：DEV-CHECKLIST-001

#### 核心功能检查
- [ ] **任务管理功能**
  - [ ] 任务创建表单验证正确
  - [ ] 任务列表展示和筛选功能正常
  - [ ] 任务编辑和删除功能正常
  - [ ] 任务状态更新功能正常

- [ ] **复习计划功能**
  - [ ] 艾宾浩斯复习计划正确生成
  - [ ] 复习时间线展示正确
  - [ ] 复习执行功能正常
  - [ ] 复习效果记录功能正常

- [ ] **负载均衡功能**
  - [ ] 负载计算准确
  - [ ] 预警提示正确显示
  - [ ] 调整建议功能正常
  - [ ] 替代日期选择功能正常

#### 技术实现检查
- [ ] **组件设计**
  - [ ] 组件职责单一，复用性好
  - [ ] Props和Events定义清晰
  - [ ] 样式符合设计规范
  - [ ] 响应式设计正确

- [ ] **状态管理**
  - [ ] Store结构合理
  - [ ] 状态更新逻辑正确
  - [ ] 异步操作处理完善
  - [ ] 错误处理机制完整

- [ ] **API集成**
  - [ ] HTTP客户端配置正确
  - [ ] API接口调用正常
  - [ ] 错误处理和重试机制完善
  - [ ] 请求响应格式符合规范

### [DEV-CHECKLIST-002] 质量保证检查
**检查ID**：DEV-CHECKLIST-002

#### 性能检查
- [ ] 页面加载时间 ≤ 2秒
- [ ] 组件渲染性能良好
- [ ] 内存使用合理
- [ ] 网络请求优化

#### 兼容性检查
- [ ] Chrome 90+ 兼容性测试通过
- [ ] Firefox 88+ 兼容性测试通过
- [ ] Safari 14+ 基础功能正常
- [ ] Edge 90+ 基础功能正常

#### 可访问性检查
- [ ] 键盘导航功能完整
- [ ] 屏幕阅读器支持
- [ ] 颜色对比度符合标准
- [ ] 语义化HTML结构

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-02-01 | 初始前端开发指南创建 | 前端架构师 | 技术负责人 |

---

**文档版本**：v1.0
**创建时间**：2025-02-01
**负责人**：前端架构师
**审核人**：技术负责人
**状态**：已发布

## 🛠️ 工具函数实现

### [DEV-UTILS-001] 日期时间工具函数
**工具ID**：DEV-UTILS-001
**修复目标**：实现组件中使用的formatDate、formatTime等函数

#### date.ts 工具函数实现
```typescript
// src/utils/date.ts
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, format = 'YYYY-MM-DD'): string => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认 'HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export const formatTime = (date: string | Date, format = 'HH:mm:ss'): string => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期时间
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 相对时间格式化
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串，如 '2小时前'
 */
export const formatRelativeTime = (date: string | Date): string => {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 生成艾宾浩斯记忆曲线复习计划
 * @param taskId 任务ID
 * @param createdAt 任务创建时间
 * @returns 复习计划数组
 */
export const generateEbbinghausSchedule = (taskId: string, createdAt: Date | string): ReviewScheduleItem[] => {
  const baseDate = dayjs(createdAt)

  // 艾宾浩斯记忆曲线复习间隔（天）
  const intervals = [1, 2, 4, 7, 15, 30, 60]

  return intervals.map((days, index) => {
    const reviewDate = baseDate.add(days, 'day')

    return {
      id: `${taskId}_review_${index + 1}`,
      taskId,
      reviewIndex: index + 1,
      intervalDays: days,
      reviewTime: reviewDate.toISOString(),
      status: 'scheduled' as const,
      effectiveness: null,
      notes: '',
      confidence: null,
      needsMoreReview: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  })
}

/**
 * 判断是否为今天
 * @param date 日期字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (date: string | Date): boolean => {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期字符串或Date对象
 * @returns 是否为本周
 */
export const isThisWeek = (date: string | Date): boolean => {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 获取日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围数组
 */
export const getDateRange = (startDate: string | Date, endDate: string | Date): string[] => {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  const dates: string[] = []

  let current = start
  while (current.isBefore(end) || current.isSame(end)) {
    dates.push(current.format('YYYY-MM-DD'))
    current = current.add(1, 'day')
  }

  return dates
}
```

### [DEV-UTILS-002] 文件处理工具函数
**工具ID**：DEV-UTILS-002
**修复目标**：实现文件上传和处理相关的工具函数

#### file.ts 工具函数实现
```typescript
// src/utils/file.ts

/**
 * 将File数组转换为FormData
 * @param files 文件数组
 * @param fieldName 字段名，默认 'files'
 * @returns FormData对象
 */
export const filesToFormData = (files: File[], fieldName = 'files'): FormData => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append(fieldName, file)
  })
  return formData
}

/**
 * 验证文件类型
 * @param file 文件对象
 * @param allowedTypes 允许的文件类型数组
 * @returns 是否为允许的类型
 */
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type)
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节）
 * @returns 是否符合大小限制
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 文件扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 生成文件预览URL
 * @param file 文件对象
 * @returns 预览URL
 */
export const createFilePreviewUrl = (file: File): string => {
  return URL.createObjectURL(file)
}

/**
 * 释放文件预览URL
 * @param url 预览URL
 */
export const revokeFilePreviewUrl = (url: string): void => {
  URL.revokeObjectURL(url)
}
```

### [DEV-UTILS-003] 验证工具函数
**工具ID**：DEV-UTILS-003
**修复目标**：实现表单验证相关的工具函数

#### validation.ts 工具函数实现
```typescript
// src/utils/validation.ts

/**
 * 邮箱验证
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 手机号验证（中国大陆）
 * @param phone 手机号
 * @returns 是否为有效手机号
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 密码强度验证
 * @param password 密码
 * @returns 密码强度等级 (1-4)
 */
export const validatePasswordStrength = (password: string): number => {
  let strength = 0

  // 长度检查
  if (password.length >= 8) strength++

  // 包含小写字母
  if (/[a-z]/.test(password)) strength++

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength++

  // 包含数字
  if (/\d/.test(password)) strength++

  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  return Math.min(strength, 4)
}

/**
 * URL验证
 * @param url URL地址
 * @returns 是否为有效URL
 */
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 身份证号验证（中国大陆）
 * @param idCard 身份证号
 * @returns 是否为有效身份证号
 */
export const validateIdCard = (idCard: string): boolean => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}
```

## 🚀 应用初始化流程

### [DEV-INIT-001] 应用启动初始化
**初始化ID**：DEV-INIT-001
**修复目标**：明确应用启动时的初始化流程和时机

#### main.ts 应用入口配置
```typescript
// src/main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import cytoscape from 'cytoscape'
import dagre from 'cytoscape-dagre'
import coseBilkent from 'cytoscape-cose-bilkent'

import App from './App.vue'
import router from './router'
import { useUserStore } from './stores/user'

// 1. 全局注册Cytoscape插件（避免组件中重复注册）
cytoscape.use(dagre)
cytoscape.use(coseBilkent)

// 2. 创建应用实例
const app = createApp(App)
const pinia = createPinia()

// 3. 注册插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 4. 应用启动后的初始化
app.mount('#app')

// 5. 异步初始化用户认证状态和路由守卫
;(async () => {
  const userStore = useUserStore()
  await userStore.initializeAuth()  // 等待认证初始化完成

  // 6. 路由守卫配置（在认证初始化完成后）
  router.beforeEach((to, from, next) => {
    // 检查需要认证的路由
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

    if (requiresAuth && !userStore.isLoggedIn) {
      // 重定向到登录页
      next('/login')
    } else {
      next()
    }
  })
})()
```

#### 初始化流程说明
1. **Cytoscape插件全局注册**：在应用启动时一次性注册所有插件
2. **Pinia状态管理初始化**：创建并注册Pinia实例
3. **路由系统初始化**：配置Vue Router
4. **UI组件库初始化**：注册Element Plus
5. **用户认证状态恢复**：从localStorage恢复登录状态
6. **路由守卫配置**：设置认证检查逻辑

#### 环境变量配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=智能学习系统
VITE_ENABLE_MOCK=true

# .env.production
VITE_API_BASE_URL=https://api.example.com/api
VITE_APP_TITLE=智能学习系统
VITE_ENABLE_MOCK=false

# .env.test
VITE_API_BASE_URL=http://test-api.example.com/api
VITE_APP_TITLE=智能学习系统（测试）
VITE_ENABLE_MOCK=false
```

**相关文档**：
- [系统整体架构设计](../02-系统设计/01-系统整体架构设计.md)
- [用户界面设计规范](../02-系统设计/05-用户界面设计规范.md)
- [API接口设计](../02-系统设计/08-API接口设计.md)
- [功能需求规格](../01-需求分析/02-功能需求规格.md)
