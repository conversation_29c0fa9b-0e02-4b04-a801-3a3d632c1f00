# [REQ-RULE-INDEX] 业务规则索引

## 📋 概述

本文档提供了艾宾浩斯记忆曲线学习管理系统所有业务规则的统一索引，确保业务规则ID的全局唯一性和可追溯性。

## 🔢 业务规则编号规范

### 编号格式
```
[REQ-RULE-XXX] 规则描述
```

### 编号分段
- **001-099**：核心业务规则（艾宾浩斯记忆曲线、任务管理）
- **100-199**：时间管理规则（负载均衡、时间预估）
- **200-299**：用户界面规则（交互、显示）
- **300-399**：数据管理规则（存储、同步、验证）
- **400-499**：扩展功能规则（思维导图、分析）
- **500-599**：系统规则（安全、性能、兼容性）

## 📊 业务规则完整索引

### 核心业务规则 (001-099)

#### 艾宾浩斯记忆曲线规则 (001-020)
- **[REQ-RULE-001]** 标准时间节点：9个固定时间间隔（5分钟、30分钟、12小时、1天、3天、1周、2周、1月、2月）
- **[REQ-RULE-002]** 复习计划生成：基于任务创建时间自动计算复习时间点
- **[REQ-RULE-003]** 自适应调整：根据复习效果调整后续复习间隔
- **[REQ-RULE-004]** 时间冲突处理：多个任务复习时间冲突时提供调整建议
- **[REQ-RULE-005]** 考虑用户学习时间偏好（避开休息时间）
- **[REQ-RULE-006]** 支持手动调整复习时间
- **[REQ-RULE-007]** 记录实际复习时间
- **[REQ-RULE-008]** 根据复习效果调整后续复习间隔
- **[REQ-RULE-009]** 支持复习笔记添加

#### 任务管理规则 (021-050)
- **[REQ-RULE-021]** 任务创建后自动生成复习计划
- **[REQ-RULE-022]** 任务标题不能重复
- **[REQ-RULE-023]** 任务状态转换：pending → active → completed（单向流转）
- **[REQ-RULE-024]** 任务生命周期：创建 → 学习 → 复习 → 完成/取消
- **[REQ-RULE-025]** 任务优先级：高优先级任务在列表中优先显示
- **[REQ-RULE-026]** 默认按创建时间倒序排列
- **[REQ-RULE-027]** 支持多条件组合筛选
- **[REQ-RULE-028]** 每页显示20个任务

#### 复习管理规则 (051-070)
- **[REQ-RULE-051]** 支持多种提醒方式：浏览器通知、应用内消息
- **[REQ-RULE-052]** 可设置提前提醒时间（5-60分钟）
- **[REQ-RULE-053]** 支持免打扰时间设置

### 时间管理规则 (100-199)

#### 负载均衡规则 (100-120)
- **[REQ-RULE-100]** 负载计算：每日任务总时长 = 新任务时长 + 复习任务时长
- **[REQ-RULE-101]** 负载等级：轻度(10-30%)、中度(30-60%)、重度(60%+)
- **[REQ-RULE-102]** 重度负载时必须提供调整建议
- **[REQ-RULE-103]** 超载时显示预警信息
- **[REQ-RULE-104]** 调度优化：系统自动建议任务时间调整，用户确认后执行

#### 时间预估规则 (121-140)
- **[REQ-RULE-121]** 新用户使用默认预估算法
- **[REQ-RULE-122]** 有历史数据的用户基于个人学习效率计算
- **[REQ-RULE-123]** 不同学科使用不同效率系数
- **[REQ-RULE-124]** 学习效率 = 有效学习内容量 / 实际学习时间
- **[REQ-RULE-125]** 过滤异常数据（过快或过慢的记录）
- **[REQ-RULE-126]** 按学科分别计算效率
- **[REQ-RULE-127]** 学习效率：基于历史数据计算个人学习效率系数

### 用户界面规则 (200-299)

#### 交互设计规则 (200-220)
- **[REQ-RULE-200]** 支持多种节点样式和颜色
- **[REQ-RULE-201]** 支持节点拖拽和连接

#### 显示规则 (221-240)
- **[REQ-RULE-221]** 最多支持1000个节点

### 数据管理规则 (300-399)

#### 数据验证规则 (300-320)
- **[REQ-CONST-001]** 任务标题：不能为空，长度1-100字符，不能包含特殊字符
- **[REQ-CONST-002]** 任务内容：不能为空，长度1-5000字符
- **[REQ-CONST-003]** 预估时间：范围1-300分钟，必须为正整数
- **[REQ-CONST-004]** 优先级和难度：范围1-5，必须为整数
- **[REQ-CONST-005]** 学科分类：必须在预定义的学科枚举列表中

#### 数据导入导出规则 (321-340)
- **[REQ-RULE-321]** 支持JSON、CSV、Excel格式
- **[REQ-RULE-322]** 导入时验证数据格式
- **[REQ-RULE-323]** 导出时保护用户隐私

### 扩展功能规则 (400-499)

#### 思维导图规则 (400-420)
- **[REQ-RULE-400]** 一个节点可关联多个任务
- **[REQ-RULE-401]** 一个任务只能关联一个节点
- **[REQ-RULE-402]** 创建任务时自动检查负载

### 系统规则 (500-599)

#### 业务逻辑验证 (500-520)
- **[REQ-CONST-006]** 时间逻辑：任务完成时间不能早于创建时间
- **[REQ-CONST-007]** 复习逻辑：复习时间不能早于任务创建时间
- **[REQ-CONST-008]** 状态逻辑：已完成的任务不能修改核心内容，只能查看和添加笔记
- **[REQ-CONST-009]** 关联逻辑：删除任务时必须处理相关的复习计划和思维导图关联

#### 系统约束条件 (521-540)
- **[REQ-CONST-010]** 数据量限制：单用户最多创建10,000个任务
- **[REQ-CONST-011]** 导图限制：单个思维导图最多包含1,000个节点
- **[REQ-CONST-012]** 文件限制：文件上传大小限制为10MB，语音录制时长限制为10分钟
- **[REQ-CONST-013]** 性能约束：任务列表单页显示不超过100个任务
- **[REQ-CONST-014]** 时间约束：复习提醒最多提前7天设置，任务预估时间不超过5小时

## 🔄 规则变更记录

| 版本 | 日期 | 变更内容 | 影响范围 |
|------|------|----------|----------|
| v1.0 | 2025-01-31 | 初始业务规则索引创建，重新编号所有规则 | 全部功能需求文档 |

## 🔗 相关文档

- [功能需求规格](./02-功能需求规格.md) - 需要根据此索引更新规则编号
- [用户场景与流程](./03-用户场景与流程.md) - 包含业务规则引用
- [非功能性需求](./04-非功能性需求.md) - 包含约束条件

## 📋 使用说明

### 新增业务规则
1. 根据规则类型选择合适的编号段
2. 在对应段内选择下一个可用编号
3. 在此索引文档中添加规则条目
4. 在相关功能文档中引用规则ID

### 修改业务规则
1. 在此索引文档中更新规则描述
2. 在所有引用该规则的文档中同步更新
3. 记录变更历史

### 删除业务规则
1. 在此索引文档中标记为已删除
2. 检查所有引用该规则的文档
3. 清理或替换相关引用

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：系统分析师  
**审核人**：产品经理  
**状态**：草稿
