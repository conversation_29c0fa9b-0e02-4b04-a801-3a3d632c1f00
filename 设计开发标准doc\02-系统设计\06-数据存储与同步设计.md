# [DES-DATA-001] 数据存储与同步设计

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的数据存储架构、[TERM-015] 数据同步机制和 [TERM-017] 离线模式实现方案，确保数据的安全性、一致性和可用性。

## 🏗️ 数据存储架构

### [DES-STORAGE-001] 混合存储架构
**设计ID**：DES-STORAGE-001  
**架构模式**：本地存储 + 云端存储 + 缓存层  
**实现需求**：[REQ-NFUNC-004] 数据安全要求

**存储架构图**：
```
┌─────────────────────────────────────────┐
│              客户端存储层               │
│  ┌─────────────┬─────────────────────┐ │
│  │ IndexedDB   │ LocalStorage        │ │
│  │ 主要数据    │ 配置和缓存          │ │
│  │ 离线支持    │ 用户偏好            │ │
│  ├─────────────┼─────────────────────┤ │
│  │ SessionStorage │ Memory Cache     │ │
│  │ 临时数据    │ 运行时缓存          │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
                    ↕ 数据同步
┌─────────────────────────────────────────┐
│              云端存储层                 │
│  ┌─────────────┬─────────────────────┐ │
│  │ 主数据库    │ 缓存数据库          │ │
│  │ MongoDB/    │ Redis               │ │
│  │ MySQL       │ 热点数据            │ │
│  ├─────────────┼─────────────────────┤ │
│  │ 文件存储    │ 备份存储            │ │
│  │ 腾讯云COS   │ 定期备份            │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
```

**存储策略**：
```typescript
// 数据存储管理器
class DataStorageManager {
  private indexedDB: IDBDatabase
  private localStorage: Storage
  private sessionStorage: Storage
  private memoryCache: Map<string, any>
  private syncQueue: SyncOperation[]

  constructor() {
    this.memoryCache = new Map()
    this.syncQueue = []
    this.initializeStorages()
  }

  private async initializeStorages(): Promise<void> {
    // 初始化IndexedDB
    await this.initIndexedDB()
    
    // 初始化其他存储
    this.localStorage = window.localStorage
    this.sessionStorage = window.sessionStorage
    
    // 设置存储配额检查
    this.setupStorageQuotaMonitoring()
  }

  private async initIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('EbbinghausLearningDB', 1)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.indexedDB = request.result
        resolve()
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // 创建对象存储
        this.createObjectStores(db)
      }
    })
  }

  private createObjectStores(db: IDBDatabase): void {
    // 任务存储
    if (!db.objectStoreNames.contains('tasks')) {
      const taskStore = db.createObjectStore('tasks', { keyPath: 'id' })
      taskStore.createIndex('userId', 'userId', { unique: false })
      taskStore.createIndex('subject', 'subject', { unique: false })
      taskStore.createIndex('status', 'status', { unique: false })
      taskStore.createIndex('createdAt', 'createdAt', { unique: false })
      taskStore.createIndex('userIdSubject', ['userId', 'subject'], { unique: false })
    }

    // 复习计划存储
    if (!db.objectStoreNames.contains('reviewSchedules')) {
      const reviewStore = db.createObjectStore('reviewSchedules', { keyPath: 'id' })
      reviewStore.createIndex('taskId', 'taskId', { unique: false })
      reviewStore.createIndex('scheduledTime', 'scheduledTime', { unique: false })
      reviewStore.createIndex('status', 'status', { unique: false })
    }

    // 思维导图存储
    if (!db.objectStoreNames.contains('mindMaps')) {
      const mindMapStore = db.createObjectStore('mindMaps', { keyPath: 'id' })
      mindMapStore.createIndex('userId', 'userId', { unique: false })
      mindMapStore.createIndex('title', 'title', { unique: false })
      mindMapStore.createIndex('createdAt', 'createdAt', { unique: false })
    }

    // 学习历史存储
    if (!db.objectStoreNames.contains('learningHistory')) {
      const historyStore = db.createObjectStore('learningHistory', { keyPath: 'id' })
      historyStore.createIndex('userId', 'userId', { unique: false })
      historyStore.createIndex('taskId', 'taskId', { unique: false })
      historyStore.createIndex('completedAt', 'completedAt', { unique: false })
    }

    // 同步状态存储
    if (!db.objectStoreNames.contains('syncStatus')) {
      const syncStore = db.createObjectStore('syncStatus', { keyPath: 'id' })
      syncStore.createIndex('entityType', 'entityType', { unique: false })
      syncStore.createIndex('lastSyncAt', 'lastSyncAt', { unique: false })
    }
  }

  // 数据操作方法
  async saveData<T>(storeName: string, data: T): Promise<void> {
    // 保存到IndexedDB
    await this.saveToIndexedDB(storeName, data)
    
    // 添加到同步队列
    this.addToSyncQueue({
      operation: 'create',
      storeName,
      data,
      timestamp: new Date()
    })
    
    // 更新内存缓存
    this.updateMemoryCache(storeName, data)
  }

  async updateData<T>(storeName: string, id: string, data: Partial<T>): Promise<void> {
    // 更新IndexedDB
    await this.updateInIndexedDB(storeName, id, data)
    
    // 添加到同步队列
    this.addToSyncQueue({
      operation: 'update',
      storeName,
      id,
      data,
      timestamp: new Date()
    })
    
    // 更新内存缓存
    this.updateMemoryCache(storeName, { id, ...data })
  }

  async deleteData(storeName: string, id: string): Promise<void> {
    // 从IndexedDB删除
    await this.deleteFromIndexedDB(storeName, id)
    
    // 添加到同步队列
    this.addToSyncQueue({
      operation: 'delete',
      storeName,
      id,
      timestamp: new Date()
    })
    
    // 从内存缓存删除
    this.removeFromMemoryCache(storeName, id)
  }

  async getData<T>(storeName: string, id: string): Promise<T | null> {
    // 首先检查内存缓存
    const cached = this.getFromMemoryCache<T>(storeName, id)
    if (cached) return cached

    // 从IndexedDB获取
    const data = await this.getFromIndexedDB<T>(storeName, id)
    if (data) {
      // 更新内存缓存
      this.updateMemoryCache(storeName, data)
    }

    return data
  }

  async queryData<T>(
    storeName: string, 
    query: DataQuery
  ): Promise<T[]> {
    // 构建查询条件
    const results = await this.queryFromIndexedDB<T>(storeName, query)
    
    // 更新内存缓存
    results.forEach(item => {
      this.updateMemoryCache(storeName, item)
    })

    return results
  }

  // IndexedDB操作方法
  private async saveToIndexedDB<T>(storeName: string, data: T): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.indexedDB.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.add(data)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  private async updateInIndexedDB<T>(
    storeName: string, 
    id: string, 
    data: Partial<T>
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.indexedDB.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      
      // 先获取现有数据
      const getRequest = store.get(id)
      
      getRequest.onsuccess = () => {
        const existingData = getRequest.result
        if (existingData) {
          // 合并数据
          const updatedData = { ...existingData, ...data, updatedAt: new Date() }
          const putRequest = store.put(updatedData)
          
          putRequest.onsuccess = () => resolve()
          putRequest.onerror = () => reject(putRequest.error)
        } else {
          reject(new Error('Data not found'))
        }
      }
      
      getRequest.onerror = () => reject(getRequest.error)
    })
  }

  private async deleteFromIndexedDB(storeName: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.indexedDB.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.delete(id)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  private async getFromIndexedDB<T>(storeName: string, id: string): Promise<T | null> {
    return new Promise((resolve, reject) => {
      const transaction = this.indexedDB.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const request = store.get(id)

      request.onsuccess = () => resolve(request.result || null)
      request.onerror = () => reject(request.error)
    })
  }

  private async queryFromIndexedDB<T>(
    storeName: string, 
    query: DataQuery
  ): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.indexedDB.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const results: T[] = []

      let request: IDBRequest

      if (query.index && query.value) {
        // 使用索引查询
        const index = store.index(query.index)
        request = index.openCursor(IDBKeyRange.only(query.value))
      } else if (query.range) {
        // 范围查询
        const index = store.index(query.range.index)
        const keyRange = IDBKeyRange.bound(query.range.lower, query.range.upper)
        request = index.openCursor(keyRange)
      } else {
        // 全表扫描
        request = store.openCursor()
      }

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const data = cursor.value
          
          // 应用过滤条件
          if (this.matchesFilter(data, query.filter)) {
            results.push(data)
          }
          
          cursor.continue()
        } else {
          // 应用排序和分页
          const sortedResults = this.applySortAndPagination(results, query)
          resolve(sortedResults)
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  // 内存缓存管理
  private getFromMemoryCache<T>(storeName: string, id: string): T | null {
    const key = `${storeName}:${id}`
    return this.memoryCache.get(key) || null
  }

  private updateMemoryCache<T>(storeName: string, data: any): void {
    const key = `${storeName}:${data.id}`
    this.memoryCache.set(key, data)
    
    // 限制缓存大小
    if (this.memoryCache.size > 1000) {
      this.cleanupMemoryCache()
    }
  }

  private removeFromMemoryCache(storeName: string, id: string): void {
    const key = `${storeName}:${id}`
    this.memoryCache.delete(key)
  }

  private cleanupMemoryCache(): void {
    // 删除最旧的50%缓存项
    const entries = Array.from(this.memoryCache.entries())
    const toDelete = entries.slice(0, Math.floor(entries.length / 2))
    
    toDelete.forEach(([key]) => {
      this.memoryCache.delete(key)
    })
  }

  // 存储配额监控
  private async setupStorageQuotaMonitoring(): Promise<void> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate()
      const usedPercentage = (estimate.usage || 0) / (estimate.quota || 1) * 100

      if (usedPercentage > 80) {
        console.warn('Storage quota usage is high:', usedPercentage.toFixed(2) + '%')
        // 触发清理机制
        this.cleanupOldData()
      }
    }
  }

  private async cleanupOldData(): Promise<void> {
    // 删除30天前的学习历史记录
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - 30)

    const oldRecords = await this.queryData<any>('learningHistory', {
      range: {
        index: 'completedAt',
        lower: new Date(0),
        upper: cutoffDate
      }
    })

    for (const record of oldRecords) {
      await this.deleteData('learningHistory', record.id)
    }
  }
}

// 数据查询接口
interface DataQuery {
  index?: string
  value?: any
  range?: {
    index: string
    lower: any
    upper: any
  }
  filter?: Record<string, any>
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  pagination?: {
    offset: number
    limit: number
  }
}

// 同步操作接口
interface SyncOperation {
  operation: 'create' | 'update' | 'delete'
  storeName: string
  id?: string
  data?: any
  timestamp: Date
  retryCount?: number
}
```

**存储特性**：
- **多层存储**：内存缓存 + IndexedDB + 云端存储
- **自动清理**：基于时间和空间的自动清理机制
- **配额监控**：实时监控存储使用情况
- **索引优化**：合理的索引设计提升查询性能

**实现需求**：[REQ-NFUNC-004] 数据安全要求  
**相关设计**：[DES-SYNC-001] 数据同步机制

## 🔄 数据同步机制

### [DES-SYNC-001] 智能数据同步
**设计ID**：DES-SYNC-001  
**同步策略**：增量同步 + 冲突检测 + 自动重试  
**实现需求**：[REQ-FUNC-015] 数据同步功能

**同步机制实现**：
```typescript
// 数据同步管理器
class DataSyncManager {
  private storageManager: DataStorageManager
  private apiClient: ApiClient
  private syncQueue: SyncOperation[]
  private syncInProgress: boolean
  private conflictResolver: ConflictResolver
  private retryScheduler: RetryScheduler

  constructor(
    storageManager: DataStorageManager,
    apiClient: ApiClient
  ) {
    this.storageManager = storageManager
    this.apiClient = apiClient
    this.syncQueue = []
    this.syncInProgress = false
    this.conflictResolver = new ConflictResolver()
    this.retryScheduler = new RetryScheduler()

    this.setupSyncScheduler()
    this.setupNetworkListener()
  }

  // 同步调度器
  private setupSyncScheduler(): void {
    // 定期同步（每5分钟）
    setInterval(() => {
      this.performSync()
    }, 5 * 60 * 1000)

    // 页面可见性变化时同步
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.performSync()
      }
    })

    // 页面卸载前同步
    window.addEventListener('beforeunload', () => {
      this.performSync(true) // 强制同步
    })
  }

  // 网络状态监听
  private setupNetworkListener(): void {
    window.addEventListener('online', () => {
      console.log('Network connection restored, starting sync...')
      this.performSync()
    })

    window.addEventListener('offline', () => {
      console.log('Network connection lost, entering offline mode...')
      this.pauseSync()
    })
  }

  // 执行同步
  async performSync(force: boolean = false): Promise<void> {
    if (this.syncInProgress && !force) return
    if (!navigator.onLine && !force) return

    this.syncInProgress = true

    try {
      // 1. 上传本地变更
      await this.uploadLocalChanges()

      // 2. 下载服务器变更
      await this.downloadServerChanges()

      // 3. 解决冲突
      await this.resolveConflicts()

      // 4. 更新同步状态
      await this.updateSyncStatus()

      console.log('Data sync completed successfully')
    } catch (error) {
      console.error('Data sync failed:', error)
      this.handleSyncError(error)
    } finally {
      this.syncInProgress = false
    }
  }

  // 上传本地变更
  private async uploadLocalChanges(): Promise<void> {
    const pendingOperations = await this.getPendingOperations()

    for (const operation of pendingOperations) {
      try {
        await this.uploadOperation(operation)
        await this.markOperationAsSynced(operation)
      } catch (error) {
        if (this.isConflictError(error)) {
          await this.handleConflict(operation, error)
        } else {
          await this.scheduleRetry(operation, error)
        }
      }
    }
  }

  private async uploadOperation(operation: SyncOperation): Promise<void> {
    switch (operation.operation) {
      case 'create':
        await this.apiClient.post(`/api/${operation.storeName}`, operation.data)
        break
      
      case 'update':
        await this.apiClient.put(
          `/api/${operation.storeName}/${operation.id}`, 
          operation.data
        )
        break
      
      case 'delete':
        await this.apiClient.delete(`/api/${operation.storeName}/${operation.id}`)
        break
    }
  }

  // 下载服务器变更
  private async downloadServerChanges(): Promise<void> {
    const lastSyncTime = await this.getLastSyncTime()
    
    // 获取各类型数据的变更
    const changes = await this.apiClient.get('/api/sync/changes', {
      params: { since: lastSyncTime.toISOString() }
    })

    for (const change of changes.data) {
      await this.applyServerChange(change)
    }
  }

  private async applyServerChange(change: ServerChange): Promise<void> {
    const localData = await this.storageManager.getData(
      change.entityType, 
      change.entityId
    )

    if (!localData) {
      // 本地没有数据，直接应用服务器变更
      await this.storageManager.saveData(change.entityType, change.data)
    } else {
      // 检查是否有冲突
      const hasConflict = await this.detectConflict(localData, change.data)
      
      if (hasConflict) {
        await this.recordConflict(change.entityType, change.entityId, {
          local: localData,
          server: change.data
        })
      } else {
        // 应用服务器变更
        await this.storageManager.updateData(
          change.entityType, 
          change.entityId, 
          change.data
        )
      }
    }
  }

  // 冲突检测和解决
  private async detectConflict(localData: any, serverData: any): Promise<boolean> {
    // 比较更新时间
    const localTime = new Date(localData.updatedAt)
    const serverTime = new Date(serverData.updatedAt)
    
    // 如果本地数据更新时间晚于服务器数据，可能存在冲突
    if (localTime > serverTime) {
      return true
    }

    // 比较关键字段
    const conflictFields = this.getConflictFields(localData.entityType)
    
    for (const field of conflictFields) {
      if (localData[field] !== serverData[field]) {
        return true
      }
    }

    return false
  }

  private async resolveConflicts(): Promise<void> {
    const conflicts = await this.getPendingConflicts()

    for (const conflict of conflicts) {
      const resolution = await this.conflictResolver.resolve(conflict)
      await this.applyConflictResolution(conflict, resolution)
    }
  }

  private async applyConflictResolution(
    conflict: DataConflict, 
    resolution: ConflictResolution
  ): Promise<void> {
    switch (resolution.strategy) {
      case 'use_local':
        // 使用本地数据，上传到服务器
        await this.uploadOperation({
          operation: 'update',
          storeName: conflict.entityType,
          id: conflict.entityId,
          data: conflict.localData,
          timestamp: new Date()
        })
        break

      case 'use_server':
        // 使用服务器数据，更新本地
        await this.storageManager.updateData(
          conflict.entityType,
          conflict.entityId,
          conflict.serverData
        )
        break

      case 'merge':
        // 合并数据
        const mergedData = this.mergeData(
          conflict.localData, 
          conflict.serverData, 
          resolution.mergeRules
        )
        
        // 更新本地和服务器
        await this.storageManager.updateData(
          conflict.entityType,
          conflict.entityId,
          mergedData
        )
        
        await this.uploadOperation({
          operation: 'update',
          storeName: conflict.entityType,
          id: conflict.entityId,
          data: mergedData,
          timestamp: new Date()
        })
        break

      case 'manual':
        // 标记为需要手动解决
        await this.markConflictForManualResolution(conflict)
        return
    }

    // 标记冲突已解决
    await this.markConflictAsResolved(conflict)
  }

  // 离线模式支持
  async enableOfflineMode(): Promise<void> {
    // 缓存关键数据
    await this.cacheEssentialData()
    
    // 设置离线标志
    await this.storageManager.saveData('appState', {
      id: 'offline_mode',
      enabled: true,
      enabledAt: new Date()
    })

    console.log('Offline mode enabled')
  }

  async disableOfflineMode(): Promise<void> {
    // 清除离线标志
    await this.storageManager.deleteData('appState', 'offline_mode')
    
    // 执行同步
    await this.performSync()

    console.log('Offline mode disabled')
  }

  private async cacheEssentialData(): Promise<void> {
    // 缓存用户的活跃任务
    const activeTasks = await this.apiClient.get('/api/tasks', {
      params: { 
        status: 'active',
        limit: 100 
      }
    })

    for (const task of activeTasks.data) {
      await this.storageManager.saveData('tasks', task)
    }

    // 缓存即将到期的复习计划
    const upcomingReviews = await this.apiClient.get('/api/reviews/upcoming', {
      params: { 
        days: 7,
        limit: 200 
      }
    })

    for (const review of upcomingReviews.data) {
      await this.storageManager.saveData('reviewSchedules', review)
    }
  }

  // 错误处理和重试
  private async handleSyncError(error: any): Promise<void> {
    if (error.code === 'NETWORK_ERROR') {
      // 网络错误，稍后重试
      this.retryScheduler.scheduleRetry(() => this.performSync(), {
        delay: 30000, // 30秒后重试
        maxRetries: 5
      })
    } else if (error.code === 'AUTH_ERROR') {
      // 认证错误，需要重新登录
      this.handleAuthError()
    } else {
      // 其他错误，记录日志
      console.error('Sync error:', error)
    }
  }

  private async scheduleRetry(
    operation: SyncOperation, 
    error: any
  ): Promise<void> {
    operation.retryCount = (operation.retryCount || 0) + 1

    if (operation.retryCount < 3) {
      // 添加到重试队列
      this.retryScheduler.scheduleRetry(
        () => this.uploadOperation(operation),
        {
          delay: Math.pow(2, operation.retryCount) * 1000, // 指数退避
          maxRetries: 3
        }
      )
    } else {
      // 超过重试次数，标记为失败
      await this.markOperationAsFailed(operation, error)
    }
  }

  // 同步状态管理
  private async updateSyncStatus(): Promise<void> {
    const syncStatus = {
      id: 'global_sync_status',
      lastSyncAt: new Date(),
      pendingOperations: this.syncQueue.length,
      conflictCount: await this.getConflictCount(),
      status: 'synced'
    }

    await this.storageManager.saveData('syncStatus', syncStatus)
  }

  async getSyncStatus(): Promise<SyncStatus> {
    const status = await this.storageManager.getData<SyncStatus>(
      'syncStatus', 
      'global_sync_status'
    )

    return status || {
      id: 'global_sync_status',
      lastSyncAt: null,
      pendingOperations: 0,
      conflictCount: 0,
      status: 'never_synced'
    }
  }
}

// 数据类型定义
interface ServerChange {
  entityType: string
  entityId: string
  operation: 'create' | 'update' | 'delete'
  data: any
  timestamp: Date
}

interface DataConflict {
  entityType: string
  entityId: string
  localData: any
  serverData: any
  detectedAt: Date
}

interface ConflictResolution {
  strategy: 'use_local' | 'use_server' | 'merge' | 'manual'
  mergeRules?: Record<string, 'local' | 'server' | 'latest'>
}

interface SyncStatus {
  id: string
  lastSyncAt: Date | null
  pendingOperations: number
  conflictCount: number
  status: 'synced' | 'syncing' | 'conflict' | 'error' | 'never_synced'
}
```

**同步特性**：
- **增量同步**：只同步变更的数据，减少网络传输
- **冲突检测**：自动检测和解决数据冲突
- **离线支持**：完整的离线模式支持
- **自动重试**：网络错误时自动重试机制
- **状态跟踪**：完整的同步状态跟踪

**实现需求**：[REQ-FUNC-015] 数据同步功能  
**相关设计**：[DES-STORAGE-001] 混合存储架构

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-01-31 | 初始数据存储与同步设计创建 | 数据架构师 | 技术负责人 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：数据架构师  
**审核人**：技术负责人  
**状态**：待审核
